# Trendify E-commerce Platform

A modern, scalable e-commerce platform built with Next.js, TypeScript, and Material-UI (MUI), featuring a modular component architecture for maximum reusability and maintainability.

## 🚀 Technology Stack

- **Framework**: Next.js 15.3.3
- **Language**: TypeScript
- **UI Library**: Material-UI (MUI) v5
- **Styling**: MUI's emotion-based CSS-in-JS
- **Icons**: MUI Icons
- **Build Tool**: Next.js with Turbopack

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout with theme provider
│   ├── page.tsx           # Homepage
│   ├── productdetail/     # Product detail page
│   └── searchpage/        # Search results page
├── components/            # Reusable components
│   ├── index.ts          # Component exports
│   ├── layout/           # Layout components
│   │   ├── Header.tsx    # Navigation header
│   │   ├── Footer.tsx    # Site footer
│   │   └── Layout.tsx    # Main layout wrapper
│   ├── sections/         # Page section components
│   │   ├── Hero.tsx      # Hero banner section
│   │   ├── FeaturedItems.tsx  # Featured products carousel
│   │   ├── ProductGrid.tsx    # Product grid layout
│   │   └── NewsletterSection.tsx  # Newsletter signup
│   └── ui/              # Basic UI components
│       ├── ProductCard.tsx    # Product display card
│       ├── SectionTitle.tsx   # Section headings
│       ├── SearchField.tsx    # Search input
│       ├── LoadingSpinner.tsx # Loading indicator
│       └── Button.tsx         # Custom button component
├── data/                 # Static data and content
│   └── homeData.ts      # Homepage content data
├── theme/               # MUI theme configuration
│   ├── theme.ts         # Theme definition
│   └── MUIThemeProvider.tsx  # Theme provider wrapper
└── types/               # TypeScript type definitions
    └── index.ts         # Shared type definitions
```

## 🎨 Design System

### Theme Configuration
The application uses a custom MUI theme with brand-specific colors:

- **Primary**: `#ed2a6b` (Brand pink)
- **Secondary**: `#1b0d12` (Dark brown)
- **Background**: `#fcf8f9` (Light pink)
- **Text Primary**: `#1b0d12`
- **Text Secondary**: `#9a4c66`

### Typography
- **Font Family**: "Plus Jakarta Sans", "Noto Sans", sans-serif
- **Responsive scaling**: Mobile-first approach with responsive font sizes
- **Font weights**: 400 (regular), 500 (medium), 700 (bold), 900 (black)

## 🧩 Component Architecture

### Layout Components

#### `Header`
- Responsive navigation with mobile-first design
- Logo, navigation menu, search, and shopping bag icons
- User avatar integration
- Customizable navigation items

```tsx
<Header
  brandName="Trendify"
  navigationItems={[
    { label: "New Arrivals", href: "/new-arrivals" },
    { label: "Featured", href: "/featured" }
  ]}
  userAvatar="https://..."
/>
```

#### `Footer`
- Responsive footer with social media links
- Configurable footer links
- Copyright information

#### `Layout`
- Main layout wrapper combining Header and Footer
- Flexible content area
- Consistent spacing and structure

### Section Components

#### `Hero`
- Full-width hero banner with background image
- Overlay text with call-to-action button
- Responsive design with mobile optimization

#### `FeaturedItems`
- Horizontal scrolling carousel
- Product card display
- Touch-friendly navigation

#### `ProductGrid`
- Responsive grid layout
- Configurable column counts for different screen sizes
- Auto-fitting grid items

#### `NewsletterSection`
- Centered call-to-action section
- Newsletter signup functionality
- Responsive button and text

### UI Components

#### `ProductCard`
- Reusable product display component
- Support for different variants (featured, grid)
- Hover effects and click handlers
- Responsive image handling

#### `Button`
- Extended MUI Button with custom variants
- Loading state support
- Brand-consistent styling

#### `SearchField`
- Search input with integrated search icon
- Keyboard navigation support
- Customizable placeholder and size

## 📱 Responsive Design

The application follows a mobile-first approach with breakpoints:

- **xs**: 0px - 599px (Mobile)
- **sm**: 600px - 959px (Tablet)  
- **md**: 960px - 1279px (Desktop)
- **lg**: 1280px+ (Large Desktop)

## 🔧 Development

### Installation

```bash
npm install
```

### Development Server

```bash
npm run dev
```

### Production Build

```bash
npm run build
npm start
```

### Linting

```bash
npm run lint
```

## 🎯 Features

### Current Features
- ✅ Responsive header with navigation
- ✅ Hero section with call-to-action
- ✅ Featured items carousel
- ✅ Product grid with responsive layout
- ✅ Newsletter signup section
- ✅ Footer with social media links
- ✅ MUI theme integration
- ✅ TypeScript support
- ✅ Component-based architecture

### Planned Features
- 🔄 Product detail pages
- 🔄 Search functionality
- 🔄 Shopping cart
- 🔄 User authentication
- 🔄 Payment integration
- 🔄 Admin dashboard

## 🏗️ Component Guidelines

### Creating New Components

1. **Location**: Place components in appropriate directories:
   - `ui/` for basic, reusable UI elements
   - `sections/` for page-specific sections
   - `layout/` for layout-related components

2. **Naming**: Use PascalCase for component names and files

3. **Props**: Define clear TypeScript interfaces for all props

4. **Styling**: Use MUI's `sx` prop for styling, leveraging the theme

5. **Exports**: Add new components to `components/index.ts`

### Example Component Structure

```tsx
'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';

interface MyComponentProps {
  title: string;
  subtitle?: string;
  onClick?: () => void;
}

const MyComponent: React.FC<MyComponentProps> = ({
  title,
  subtitle,
  onClick
}) => {
  return (
    <Box
      onClick={onClick}
      sx={{
        p: 2,
        borderRadius: 2,
        bgcolor: 'background.paper',
        cursor: onClick ? 'pointer' : 'default',
      }}
    >
      <Typography variant="h2">{title}</Typography>
      {subtitle && (
        <Typography variant="body2" color="text.secondary">
          {subtitle}
        </Typography>
      )}
    </Box>
  );
};

export default MyComponent;
```

## 🤝 Contributing

1. Follow the established component architecture
2. Use TypeScript for type safety
3. Follow the design system guidelines
4. Write responsive components
5. Include proper error handling
6. Add components to the index exports

## 📄 License

This project is licensed under the MIT License.
