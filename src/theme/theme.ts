import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#ed2a6b',
      light: '#f3e7eb',
      dark: '#9a4c66',
      contrastText: '#fcf8f9',
    },
    secondary: {
      main: '#1b0d12',
      light: '#9a4c66',
      dark: '#1b0d12',
      contrastText: '#fcf8f9',
    },
    background: {
      default: '#fcf8f9',
      paper: '#ffffff',
    },
    text: {
      primary: '#1b0d12',
      secondary: '#9a4c66',
    },
  },
  typography: {
    fontFamily: '"Plus Jakarta Sans", "Noto Sans", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 900,
      lineHeight: 1.2,
      letterSpacing: '-0.033em',
    },
    h2: {
      fontSize: '1.5rem',
      fontWeight: 700,
      lineHeight: 1.3,
      letterSpacing: '-0.015em',
    },
    h3: {
      fontSize: '1.375rem',
      fontWeight: 700,
      lineHeight: 1.3,
      letterSpacing: '-0.015em',
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 700,
      textTransform: 'none',
      letterSpacing: '0.015em',
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          padding: '10px 16px',
          fontSize: '0.875rem',
          fontWeight: 700,
          letterSpacing: '0.015em',
          textTransform: 'none',
        },
        containedPrimary: {
          backgroundColor: '#ed2a6b',
          color: '#fcf8f9',
          '&:hover': {
            backgroundColor: '#d12456',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 12,
            backgroundColor: '#f3e7eb',
            '& fieldset': {
              borderColor: 'transparent',
            },
            '&:hover fieldset': {
              borderColor: '#e7cfd7',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#ed2a6b',
            },
          },
        },
      },
    },
  },
});

export default theme;
