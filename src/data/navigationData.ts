export interface NavigationItem {
  label: string;
  href: string;
}

export const mainNavigationItems: NavigationItem[] = [
  { label: "New Arrivals", href: "/searchpage?category=new" },
  { label: "Featured", href: "/searchpage?category=featured" },
  { label: "Sale", href: "/searchpage?category=sale" },
  { label: "About Us", href: "/contactus" },
];

export const userNavigationItems: NavigationItem[] = [
  { label: "Orders", href: "/orderhistory" },
  { label: "Contact", href: "/contactus" },
  { label: "Shipping", href: "/shipping" },
];

export const footerNavigationItems: NavigationItem[] = [
  { label: "Contact Us", href: "/contactus" },
  { label: "Order History", href: "/orderhistory" },
  { label: "Shipping & Returns", href: "/shipping" },
  { label: "Search", href: "/searchpage" },
  { label: "Home", href: "/" },
];

export const allNavigationItems: NavigationItem[] = [
  ...mainNavigationItems,
  ...userNavigationItems,
];

// Helper function to get navigation items for a specific page
export const getNavigationItems = (): NavigationItem[] => {
  return mainNavigationItems;
};
