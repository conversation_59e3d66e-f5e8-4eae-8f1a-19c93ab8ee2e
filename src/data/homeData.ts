import { Product, HeroData, NewsletterData } from '@/types';

export const featuredItems: Product[] = [
  {
    id: "1",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuCNKyRDhfA5j_K8jhTDicjyQwNgX4GSFgWskX2NnMfp3ANVDgmlGz5TT28SAdk9BMWmkcAlvYpJyme4afD96Is2eFITtG_HGJ56O3t67aIa5lBWBbvWe05uS3AJSLGW68hekrmDbkt8kIcPbrQ0zLY-Jrp-znJjjRiiqmDybBCMyc3cw0qZn9tWUKeIaHHZsFSYVWMt6a3v1tgUk-sqR5rgQV2AAA2Q8TGXGUFKyhFyJj5duPKgr1wHu3RueWkx2DgB34hvjDaS9-cY",
    title: "Summer Breeze Dress",
    description: "Perfect for sunny days",
    category: "dresses",
    price: 89.99,
  },
  {
    id: "2",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuDWlk8sW9zuQPrKuxOAwn003cWKvp4wRgwNrHKLCySSvWhXeHfJI8suvNiYZeZ31qfjjlrvAmo8vbhPII9Hw6sC61yNuxsdfBnv4QHgXsbIQ070t0VqlcKTbd309JtTTNxuv4bp58rnaXNbCvUKRuhn8MaDm_-qNsH-nnZuEDYrMi0y3Bqcp7HdWbwqW-DysK2Qp1Z3IA1ZLnS3MxGC_5DPyAGZaRAXPZcA2JZnp0c1Zcddduv5A-9phzes01BR62SxKmGGtiqeFsBD",
    title: "Urban Edge Streetwear",
    description: "Cool and comfortable",
    category: "streetwear",
    price: 65.99,
  },
  {
    id: "3",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuByo4tIGUSM4pgNtU1W78WlU1DH_uRD-Hjnp3LVEsNX-8Wp6aT75rBLLaLS2frbV-7DcDKW-bl8ChgEtNS9TZ2ltGQhN9hjC99-qmHW5M9BwwuCQdx3IAFq_At55P0V7PEHL7vVYRz7MJkXlipxx2_Vtgsak7n80RPlXfHbAqNaWmO5qIZhetzkOSL2TiNWSLBYnAGvDG5JuqfNr2w1NU11f0AUmCNciB-D-QXH25Q55R9-rFRoHebh_La3pQJv_1puUIIrRQArnDjs",
    title: "Chic Casual Top",
    description: "Versatile and stylish",
    category: "tops",
    price: 42.99,
  },
];

export const newArrivals: Product[] = [
  {
    id: "4",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuDLLBc7QNDrfF4SzTESSXTviVCSgB-MPVj7Xk-5nFhzMHl59y-GO1FF1lAO_5XVyW_vTDoAlpIO2ma9Df2zE-UE2qr7YIeeLioC7P8eedVZPTEBEmHELYPIwF5wkZJfhW_oISBdqDzf_5sLrfIl1myYReS4XdCq6P0UhB7Qf_jo0eTFiGcvg0bRyYMBAKre7KRi_NQz2yf0CcjkZeJ7uNLv1kYk74wHk2TAof06282EpDJT1DtvU7XvxIBdmT-tEHvtS5J58dEwd0hg",
    title: "Graphic Tee Collection",
    description: "Express your style with our new graphic tees",
    category: "tops",
    price: 29.99,
  },
  {
    id: "5",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuAvUjNcN7b6otdX5FL7lnFnIR1iSz7Y9ks3dUsPgJI7GiCTOpD4kxodHuY7-Yd1LTzVd8FhuAfHNBHKKmA3xzU7OJXDUt-SOSM9xM6B4WCauWN663TWNjLK7o5pA56cbJoIxqnZQ0ljMd9h-ujsz9YSCTVkkW83s82FnU9hYsh9Rhi-vMzlYgkrytN7gtx7ujLrs68r55tphUXjDuWaNFdEJwG5dLjFxlRhxf5U2NSkY0I1Nvn8oj9DShsfCXCp58mEDRPbcKdlmTuX",
    title: "Denim Dreams",
    description: "Find your perfect fit with our latest denim collection",
    category: "denim",
    price: 79.99,
  },
  {
    id: "6",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuCI5lGhlP-prPwgLjP3w0-DgMOZDKyR6e-ELBm-UK1TXE2xDri0p3H3vf6Rq1QuQV_cxN52ePqk0snhU6MVPea8UhzVJwZf8hvF2AjH9Qz9rhCLzbeNSQqDt8wptK9UB4mYzcI3TixxVQ164o1EghAh48an9nlGIknV4_We_X3n23MyZPD7fBlffSl4WerLtB1adMmfgO2CSoq6MHcHLr31kq1aaMLPo08UCOUp1c4ilotpndqAxkabLKXST3bh2_oOMakckKnya_QY",
    title: "Athleisure Essentials",
    description: "Comfort and style for your active lifestyle",
    category: "activewear",
    price: 55.99,
  },
  {
    id: "7",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuB9_xyERZFTXT3OnwbuVs1R_SYh3HAsMJtOCYlrsxFM0SoreRCSwV0b3RkMIx5Bz09gcC-Vvqf08Aydf45PX0zcS0w4sxKyFOXhxNQsZBJglIAXCialioqYeZWHvWW06FbZOlPQG8-1ot5PAQxrRg9IF7v1gUr6VtcoJc9fVyHyga7NUy-JnpyQFncJhpj7oZPvchmd68PmoR7L-dDxTy5BqcnQWVTh-7W8zwR5950Qfhy3rwHIOFsAJPrsPIgteaXYMdRiW_atvCYW",
    title: "Festival Ready Outfits",
    description: "Stand out from the crowd with our festival outfits",
    category: "festival",
    price: 99.99,
  },
];

export const heroData: HeroData = {
  backgroundImage: "https://lh3.googleusercontent.com/aida-public/AB6AXuCDujp6evlJQd78UxTQekpNqsIm1YZtW9oc4YviJ4mHSxzuXAL6Ury2eFv3RIuya_t9AX0yPjWX4eWRqvFdmTI_kViA-5Iv7WJfezzAUU3y63dFvavTCvBbsk1HArOHsux6orAivgZp-RTeTQbcfvzf_UEW9NkRoMSZQg_P76H9ikg4dOnLOVE7kwEPs6Mz_uDXS6dZDt646ocj9MCm75zHEYW1pGKAULktmIwIgoQ5IdwDm6VYhOkRBx1N3TDEoi4cAHLM7wHsawjq",
  title: "Express Yourself with Trendify",
  subtitle: "Discover the latest styles and trends that speak to your unique personality. Shop now and redefine your wardrobe.",
  ctaText: "Shop New Arrivals",
};

export const newsletterData: NewsletterData = {
  title: "Join the Trendify Community",
  description: "Stay updated on the latest trends, exclusive offers, and more.",
  ctaText: "Sign Up Now",
};
