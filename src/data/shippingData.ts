export const shippingOptions = [
    {
        id: 'standard',
        title: 'Standard Shipping',
        description: '3-5 business days',
        price: 'Free',
    },
    //   {
    //     id: 'express',
    //     title: 'Express Shipping',
    //     description: '2-3 business days',
    //     price: '$9.99',
    //   },
    //   {
    //     id: 'priority',
    //     title: 'Priority Shipping',
    //     description: '1-2 business days',
    //     price: '$14.99',
    //   },
];

export const shippingPageData = {
    title: 'Shipping & Returns',
    breadcrumbs: [
        { label: 'Home', href: '/' },
        { label: 'Shipping & Returns', href: '/shipping' },
    ],
    sections: {
        shipping: {
            title: 'Shipping Options',
            description: 'We offer several shipping options to meet your needs. Choose the one that best fits your timeline and budget.',
        },
        delivery: {
            title: 'Delivery Times',
            description: 'Delivery times vary depending on the shipping method selected and your location. Please note that delivery times are estimates and may be subject to delays due to unforeseen circumstances.',
        },
        returns: {
            title: 'Returns & Exchanges',
            description: `We want you to be completely satisfied with your purchase. Returns or exchanges are only accepted if the product delivered is incorrect or damaged. To be eligible, please report the issue within 7 days of delivery. The item must be unused, in its original condition, and with all original packaging and tags attached.`,
        },
    },
};
