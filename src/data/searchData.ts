import { Product } from '@/types';

export const searchResults: Product[] = [
  {
    id: "search-1",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuB36wS2o2HXXzmk0PSugHU3Rw9h5w1yxts4XRISOe-yonhi7Yv3zVaPlRpUfX8UkziSOzbBnm6g-MD1RCav__AiKxEEBHe0K3UFfAIxfV7K4tTNTVo5oK081f1kkWyQtQZqizpiRn2v2PHAFjnfm7GwFvIN7rYvKTYVwctJMgXMpSRsriNe8-0SLSpkXNAUtwpML0hVF4jPQh_Q14MQu5gLVNU_mppQdzhztxjbJm8DPkoD6xAKK9gm1D5N-PxTFnrPr-TQONtiOOAL",
    title: "Graphic Tee",
    description: "Cool graphic design",
    category: "tops",
    price: 24.99,
  },
  {
    id: "search-2",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuD6elxEJmTNMFbksYrfOpTCQFsQIqMuSkpSJ2Pz9VRoJJ3sAJjrw5L727m0C3KPCmzaOSM_LZ-4E2qCE81h_po3D4SlRqbYotgmsELRt74_sBke5aPcdELL-cjv-KkcB9jasyVdIpMuMAokDykA_GCTgiU0kZBinp2391_td_vWyhOxlxuhR5eWiCQ7xekdybzXZJ86srFz_sDltu0Atp4JSNnVLFwBo6h_W63--2Ja9eL028WKGN49AaR1TiLQd9FyL9y-9_DWt7zh",
    title: "Striped T-Shirt",
    description: "Classic striped pattern",
    category: "tops",
    price: 29.99,
  },
  {
    id: "search-3",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuD88jsiUZKhCuj8AsPalQp0GgcwISHfl3gQWeALzuzgbiCWq6sEmkmLNTlp4hz2oW8moMfIzPKHA2PzcP03e3XlCiU19nJbic_OYJNSmOBpRj1tEyFi3L4NOGkDFwinBv3Lgm-dCxd2cknljzQp3zgim-JwfEky30ZVrX5H9sFNS8-dfSKPe5aG0mYBHrsWLWlsCXc1SpWTnX0XeVouvJZhVOSbm_NK5SQC_y9TaYxtpSq2R5GT7wPtusIzGe46WD3T0Fuq22dHjxTH",
    title: "Oversized Tee",
    description: "Comfortable oversized fit",
    category: "tops",
    price: 34.99,
  },
  {
    id: "search-4",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuDj_1eF-I4sYC76zUa7TSPf5khtbm2Dd8p_VP-tTUAclwnVM8qI0xBhrXHFxxSzhHHl4yhQwX5GEmKkp_2SapsY5kOgSqIc-AEh0vkBPVzANLLw-zg1gnXWmk4qrEJ28jEZE_Y-0H7bKmOA6IIp9wuLLYiSno5F0nDQ_1zf4Y-6WqytF93bNiaI1L6JjgLj-0KqYMraqd6AjLa2thrubApo8XLGDi-lps7sNz-fegZN7s9pLJX87bCgLVRxH7JSngY5j9liUzCDATy2",
    title: "Vintage Tee",
    description: "Retro vintage style",
    category: "tops",
    price: 39.99,
  },
  {
    id: "search-5",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuD7X-CTR_K9EBJ882qY3hRQ4SiW8lBfKzns5OnR5bpo3Mj_uAh1IuonSX8nvsTP-g45Blw5EIkfClq-HYJ2sXQzEorI9qbi99xczCPV4qOFkc9K0WL43tl1WXwx30UDZVgQIXklTupxFqA6T_YHo4fAGZb-K8e8CpzGaExIvjJ3D07UTOEKZQgh4DTxhYGb0nDJCuhkHwyBdRqiXa9NBsZWln_C2KpsY9rMHK1ZuIDkFEwdXj9LWg3Ud_PF0LBqIICgHSKlzr2pRJN8",
    title: "Cropped Tee",
    description: "Trendy cropped fit",
    category: "tops",
    price: 27.99,
  },
  {
    id: "search-6",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuCBBTrfwPtiSu4B0vj9n79kR8AYywNMeIDBFPYX4Gz_d7UAlJjVJR6-jh7wLV5KZtTrpawnpvZHeMa9Yq4pobmdz5RgLUO8IkF1G4jG8fy7_Cudoem03bPvByx-bDU9CO9cxzbmggAObk7HhrdbsGGcWym1IMNPe3xqZadSrceuZjOACwqarmpxaC60lbE5mu4Ejg7n_ppAqPwe2tdXiz-id27zWojvWkxtt14X6orqCjHennkujsQlfs2qPwwMq41at_cMev6yk3hP",
    title: "Long Sleeve Tee",
    description: "Classic long sleeve",
    category: "tops",
    price: 32.99,
  },
  {
    id: "search-7",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuCrFkLEaQ6QF8zgPN3dSHK-r2B9TXkyvYyXa4hTDm2yWprVatUvivffYNJe0wXLAKmxGAYN1_eJE1Nxt6fv8MwMBDch4tpeVIdqcokeIq0Th-DT7YWgBn_6r9VGATdfFMqJlg8rrbnkMqElms3wxUHNTSv3VtSbmJ0x_FP-EWuJBR2WaBas7U0NSF5gY0nn4Lvmn29wzhYSx-Tad8UNgqsLKf_FX_RmFghAbkS0rLH-YLPP7JfBkw5_E0kTqjY6Lj6e3zMI8WbiXKMt",
    title: "Tie-Dye Tee",
    description: "Colorful tie-dye pattern",
    category: "tops",
    price: 36.99,
  },
  {
    id: "search-8",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuCITzzIxbrttGSEW8SHbmiNpSO5n49Up_l9s_JgZABld6qKBQ_fSKH-PFM5bFlL1FCRsEpYFM54FjcltC3lcduwXsWaMOpHTel49rvyVMhyBzvzVjgpozYLjC9ejOp5nM6qXCLFAimLh7h8r18ABmiyQo1FPSi4QlxlDMgrVsApqBgB31hRw30TAjHW48UQZdu0q4vjnW6lF6ZAzBSGRB1CwfPHhz4yay8z8XaWOhI3c-aA4_wqBh6Q85JkVz1jZiUBxmEJZweLnylN",
    title: "Band Tee",
    description: "Music band merchandise",
    category: "tops",
    price: 28.99,
  },
  {
    id: "search-9",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuBjAjG7WXZgdYM--69OHrmpbx8YN70nHcv7TmLFAYzNejqOWao7jWGZ9pCqt8eW4gIyLiDXPZzBRz9CR4zuJBRdt7jTCuGDEkwc230MYnduAVtmS8PCygfmYV58vWhD83UVrC1y0UyDoJlW0dOoAcbkJafUheERHI0XTaSsQIck7MrUE-fHcyKDVh39z4-2I2u5-F6jk0E4JjDeGKOE4qJi0zg7CHC5skOvaEJ_qXj8VfW3Wkoy1DlZwNfqnuJ8LVKBraAQ0WEYMRGR",
    title: "Pocket Tee",
    description: "Simple pocket design",
    category: "tops",
    price: 25.99,
  },
  {
    id: "search-10",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuCLUDBbEb778I7p3YikJEGADtbROsdOCwN8meuo8EUoEGRtDh7zJ4KyADcMtGGww5xTIceZqlqYbQcxvE6nYMYkCWyMSaz9tGzOz2ynx3VSkgx-FHSu0V02KMM3DNOG2Fw07Xj5mQddlGKVwwLpWVFmGrUJEoFcIQsJFtudckzfD9kK89AOdytpLgSscsSAq-akIIfK-1dne7kDZZyqhf9e2nI_jYpvAXtTRFCSfofUBpEl61L6KTJpb2uJGLSRmSVsLouVIGlW4IE5",
    title: "Ringer Tee",
    description: "Classic ringer style",
    category: "tops",
    price: 30.99,
  },
  {
    id: "search-11",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuANEqYAscds0Zo3IbjzmQA4rRLoOH4PDD-OsxXQgEAHo5ro_emGZ8vpP_JLhj_2zs-tTL1HJvs5IN07-6TaMWVZnRhgSW2GAYGRTD5wOSDuASYbTo3D4jAqRe8AxDZbPNDf5Joc59bDIybbqCClAr7_gUDs7S99vurXMnZc3J4ETDVpUmhtc_mALbfSgxN3wtSVisLcQQwLqbMoVYsHqXr8ll_zuPCZGHZ-rtSGH8-uZMlp3neE5FXYtvE2owLNiJQYhclPWO3LdxLN",
    title: "Raglan Tee",
    description: "Athletic raglan sleeves",
    category: "tops",
    price: 33.99,
  },
  {
    id: "search-12",
    image: "https://lh3.googleusercontent.com/aida-public/AB6AXuDCn_qzugRjyr6MBQh48-H4L4wkesxg6iPbHNyJNwL0AMK1BwHaPCqTALMHLGDMGlMgDHvQpkUz48yL_6UXnuwtyquBctvjkXn-srOgyKlFUmp1JqmBFFQfgoR0rdyMiZTI9T2Vpx_vfF7Goc2JcnU9K9FucGi3r-Oo_xtgyB1FSyU1oSwDOCctLMcNX13Us8DuHFOiM3_sFZ-70Ssm55LiBIJ7hFoswWJbu74yL9-h9z28SSaMS_aH1GnRfph-ObC66cWKPLJoWO4X",
    title: "Henley Tee",
    description: "Button-neck henley style",
    category: "tops",
    price: 37.99,
  },
];

export const filterChips = [
  { id: "all", label: "All", active: true },
  { id: "tops", label: "Tops", active: false },
  { id: "bottoms", label: "Bottoms", active: false },
  { id: "dresses", label: "Dresses", active: false },
  { id: "outerwear", label: "Outerwear", active: false },
];

export const sortOptions = [
  { value: "recommended", label: "Recommended" },
  { value: "price-low", label: "Price: Low to High" },
  { value: "price-high", label: "Price: High to Low" },
  { value: "newest", label: "Newest First" },
  { value: "rating", label: "Highest Rated" },
];
