import { builder } from '../lib/builder'

// Import all type definitions to register them with the builder
import './types/common'
import './types/customer'
import './types/product'
import './types/order'
import './types/wishlist'
import './types/additional'

// Import queries and mutations
import './queries'
import './mutations'

// Build and export the schema
export const schema = builder.toSchema()
