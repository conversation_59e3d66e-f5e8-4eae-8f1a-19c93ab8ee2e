import { builder } from '../../lib/builder'
import { SortDirection, OrderStatus } from './common'

// Order type
export const Order = builder.prismaObject('Order', {
  fields: (t) => ({
    id: t.exposeID('id'),
    orderDate: t.expose('orderDate', { type: 'DateTime' }),
    totalAmount: t.exposeFloat('totalAmount'),
    status: t.expose('status', { type: OrderStatus }),
    
    // Relations
    customer: t.relation('customer'),
    items: t.relation('items'),
    paymentTransaction: t.relation('paymentTransaction', { nullable: true }),
    
    // Computed fields
    itemCount: t.int({
      resolve: async (order, args, { prisma }) => {
        return prisma.orderItem.count({
          where: { orderId: order.id },
        })
      },
    }),
    totalQuantity: t.int({
      resolve: async (order, args, { prisma }) => {
        const result = await prisma.orderItem.aggregate({
          where: { orderId: order.id },
          _sum: { quantity: true },
        })
        return result._sum.quantity || 0
      },
    }),
  }),
})

// Order Item type
export const OrderItem = builder.prismaObject('OrderItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    quantity: t.exposeInt('quantity'),
    priceAtPurchase: t.exposeFloat('priceAtPurchase'),
    
    // Relations
    order: t.relation('order'),
    productVariant: t.relation('productVariant'),
    
    // Computed fields
    subtotal: t.float({
      resolve: (item) => item.quantity * item.priceAtPurchase,
    }),
  }),
})

// Payment Method type
export const PaymentMethod = builder.prismaObject('PaymentMethod', {
  fields: (t) => ({
    id: t.exposeID('id'),
    provider: t.exposeString('provider'),
    type: t.exposeString('type'),
    last4: t.exposeString('last4'),
    isDefault: t.exposeBoolean('isDefault'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    
    // Relations
    customer: t.relation('customer'),
    transactions: t.relation('transactions'),
  }),
})

// Payment Transaction type
export const PaymentTransaction = builder.prismaObject('PaymentTransaction', {
  fields: (t) => ({
    id: t.exposeID('id'),
    amount: t.exposeFloat('amount'),
    status: t.exposeString('status'),
    transactionId: t.exposeString('transactionId'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    
    // Relations
    paymentMethod: t.relation('paymentMethod'),
    order: t.relation('order', { nullable: true }),
  }),
})

// PromoCode type
export const PromoCode = builder.prismaObject('PromoCode', {
  fields: (t) => ({
    id: t.exposeID('id'),
    code: t.exposeString('code'),
    discountType: t.exposeString('discountType'),
    discountValue: t.exposeFloat('discountValue'),
    expiration: t.expose('expiration', { type: 'DateTime' }),
    usageLimit: t.exposeInt('usageLimit', { nullable: true }),
    usedCount: t.exposeInt('usedCount'),
    isActive: t.exposeBoolean('isActive'),
    
    // Relations
    usages: t.relation('usages'),
    
    // Computed fields
    isExpired: t.boolean({
      resolve: (promoCode) => promoCode.expiration < new Date(),
    }),
    isValid: t.boolean({
      resolve: (promoCode) => {
        const now = new Date()
        return (
          promoCode.isActive &&
          promoCode.expiration > now &&
          (!promoCode.usageLimit || promoCode.usedCount < promoCode.usageLimit)
        )
      },
    }),
    remainingUses: t.int({
      resolve: (promoCode) => {
        if (!promoCode.usageLimit) return -1 // Unlimited
        return Math.max(0, promoCode.usageLimit - promoCode.usedCount)
      },
    }),
  }),
})

// PromoCode Usage type
export const PromoCodeUsage = builder.prismaObject('PromoCodeUsage', {
  fields: (t) => ({
    id: t.exposeID('id'),
    usedAt: t.expose('usedAt', { type: 'DateTime' }),
    
    // Relations
    promoCode: t.relation('promoCode'),
    customer: t.relation('customer'),
  }),
})

// Input types for Order operations
export const OrderItemCreateInput = builder.inputType('OrderItemCreateInput', {
  fields: (t) => ({
    productVariantId: t.string({ required: true }),
    quantity: t.int({ required: true, validate: { min: 1 } }),
  }),
})

export const OrderCreateInput = builder.inputType('OrderCreateInput', {
  fields: (t) => ({
    customerId: t.string({ required: true }),
    items: t.field({
      type: [OrderItemCreateInput],
      required: true,
    }),
    promoCodeId: t.string(),
  }),
})

export const OrderUpdateInput = builder.inputType('OrderUpdateInput', {
  fields: (t) => ({
    status: t.field({ type: OrderStatus }),
  }),
})

export const OrderFilterInput = builder.inputType('OrderFilterInput', {
  fields: (t) => ({
    customerId: t.string(),
    status: t.field({ type: OrderStatus }),
    orderDateAfter: t.field({ type: 'DateTime' }),
    orderDateBefore: t.field({ type: 'DateTime' }),
    minAmount: t.float(),
    maxAmount: t.float(),
  }),
})

// Sorting options
export const OrderSortField = builder.enumType('OrderSortField', {
  values: ['orderDate', 'totalAmount', 'status'] as const,
})

export const OrderOrderByInput = builder.inputType('OrderOrderByInput', {
  fields: (t) => ({
    field: t.field({ type: OrderSortField, required: true }),
    direction: t.field({ type: SortDirection, required: true }),
  }),
})

// Payment method input types
export const PaymentMethodCreateInput = builder.inputType('PaymentMethodCreateInput', {
  fields: (t) => ({
    customerId: t.string({ required: true }),
    provider: t.string({ required: true }),
    type: t.string({ required: true }),
    last4: t.string({ required: true, validate: { minLength: 4, maxLength: 4 } }),
    isDefault: t.boolean(),
  }),
})

// PromoCode input types
export const PromoCodeCreateInput = builder.inputType('PromoCodeCreateInput', {
  fields: (t) => ({
    code: t.string({ required: true, validate: { minLength: 3 } }),
    discountType: t.string({ required: true }),
    discountValue: t.float({ required: true, validate: { min: 0 } }),
    expiration: t.field({ type: 'DateTime', required: true }),
    usageLimit: t.int({ validate: { min: 1 } }),
  }),
})
