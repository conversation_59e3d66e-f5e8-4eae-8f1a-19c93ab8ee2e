import { builder } from '../../lib/builder'
import { SortDirection } from './common'

// Customer Prisma object type
export const Customer = builder.prismaObject('Customer', {
  fields: (t) => ({
    id: t.exposeID('id'),
    email: t.exposeString('email'),
    firstName: t.exposeString('firstName'),
    lastName: t.exposeString('lastName'),
    phone: t.exposeString('phone', { nullable: true }),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    
    // Relations
    wishlist: t.relation('wishlist', { nullable: true }),
    reviews: t.relation('reviews'),
    orders: t.relation('orders'),
    paymentMethods: t.relation('paymentMethods'),
    promoCodeUsages: t.relation('promoCodeUsages'),
    
    // Computed fields
    fullName: t.string({
      resolve: (customer) => `${customer.firstName} ${customer.lastName}`,
    }),
    orderCount: t.int({
      resolve: async (customer, args, { prisma }) => {
        return prisma.order.count({
          where: { customerId: customer.id },
        })
      },
    }),
  }),
})

// Input types for Customer operations
export const CustomerCreateInput = builder.inputType('CustomerCreateInput', {
  fields: (t) => ({
    email: t.string({ required: true, validate: { email: true } }),
    firstName: t.string({ required: true, validate: { minLength: 1 } }),
    lastName: t.string({ required: true, validate: { minLength: 1 } }),
    phone: t.string({ validate: { regex: /^\+?[\d\s-()]+$/ } }),
  }),
})

export const CustomerUpdateInput = builder.inputType('CustomerUpdateInput', {
  fields: (t) => ({
    email: t.string({ validate: { email: true } }),
    firstName: t.string({ validate: { minLength: 1 } }),
    lastName: t.string({ validate: { minLength: 1 } }),
    phone: t.string({ validate: { regex: /^\+?[\d\s-()]+$/ } }),
  }),
})

export const CustomerFilterInput = builder.inputType('CustomerFilterInput', {
  fields: (t) => ({
    email: t.string(),
    firstName: t.string(),
    lastName: t.string(),
    createdAfter: t.field({ type: 'DateTime' }),
    createdBefore: t.field({ type: 'DateTime' }),
  }),
})

// Customer connection for pagination
export const CustomerConnection = builder.connectionObject({
  type: Customer,
  name: 'CustomerConnection',
})

// Sorting options
export const CustomerSortField = builder.enumType('CustomerSortField', {
  values: ['firstName', 'lastName', 'email', 'createdAt'] as const,
})

export const CustomerOrderByInput = builder.inputType('CustomerOrderByInput', {
  fields: (t) => ({
    field: t.field({ type: CustomerSortField, required: true }),
    direction: t.field({ type: SortDirection, required: true }),
  }),
})
