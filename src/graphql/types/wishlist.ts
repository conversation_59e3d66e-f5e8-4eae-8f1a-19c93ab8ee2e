import { builder } from '../../lib/builder'
import { SortDirection } from './common'

// Wishlist type
export const Wishlist = builder.prismaObject('Wishlist', {
  fields: (t) => ({
    id: t.exposeID('id'),
    
    // Relations
    customer: t.relation('customer'),
    items: t.relation('items'),
    
    // Computed fields
    itemCount: t.int({
      resolve: async (wishlist, args, { prisma }) => {
        return prisma.wishlistItem.count({
          where: { wishlistId: wishlist.id },
        })
      },
    }),
    totalValue: t.float({
      resolve: async (wishlist, args, { prisma }) => {
        const items = await prisma.wishlistItem.findMany({
          where: { wishlistId: wishlist.id },
          include: { productVariant: true },
        })
        return items.reduce((total: number, item) => total + item.productVariant.price, 0)
      },
    }),
  }),
})

// Wishlist Item type
export const WishlistItem = builder.prismaObject('WishlistItem', {
  fields: (t) => ({
    id: t.exposeID('id'),
    addedAt: t.expose('addedAt', { type: 'DateTime' }),
    
    // Relations
    wishlist: t.relation('wishlist'),
    productVariant: t.relation('productVariant'),
  }),
})

// Review type
export const Review = builder.prismaObject('Review', {
  fields: (t) => ({
    id: t.exposeID('id'),
    rating: t.exposeInt('rating'),
    comment: t.exposeString('comment', { nullable: true }),
    images: t.exposeStringList('images'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    
    // Relations
    customer: t.relation('customer'),
    product: t.relation('product'),
    
    // Computed fields
    hasImages: t.boolean({
      resolve: (review) => review.images.length > 0,
    }),
    isRecentlyAdded: t.boolean({
      resolve: (review) => {
        const threeDaysAgo = new Date()
        threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
        return review.createdAt > threeDaysAgo
      },
    }),
  }),
})

// Input types for Wishlist operations
export const WishlistItemAddInput = builder.inputType('WishlistItemAddInput', {
  fields: (t) => ({
    productVariantId: t.string({ required: true }),
  }),
})

export const WishlistItemRemoveInput = builder.inputType('WishlistItemRemoveInput', {
  fields: (t) => ({
    wishlistItemId: t.string({ required: true }),
  }),
})

// Input types for Review operations
export const ReviewCreateInput = builder.inputType('ReviewCreateInput', {
  fields: (t) => ({
    productId: t.string({ required: true }),
    customerId: t.string({ required: true }),
    rating: t.int({ 
      required: true, 
      validate: { min: 1, max: 5 } 
    }),
    comment: t.string({ validate: { maxLength: 1000 } }),
    images: t.stringList({ validate: { maxLength: 5 } }),
  }),
})

export const ReviewUpdateInput = builder.inputType('ReviewUpdateInput', {
  fields: (t) => ({
    rating: t.int({ validate: { min: 1, max: 5 } }),
    comment: t.string({ validate: { maxLength: 1000 } }),
    images: t.stringList({ validate: { maxLength: 5 } }),
  }),
})

export const ReviewFilterInput = builder.inputType('ReviewFilterInput', {
  fields: (t) => ({
    productId: t.string(),
    customerId: t.string(),
    minRating: t.int({ validate: { min: 1, max: 5 } }),
    maxRating: t.int({ validate: { min: 1, max: 5 } }),
    hasComment: t.boolean(),
    hasImages: t.boolean(),
    createdAfter: t.field({ type: 'DateTime' }),
    createdBefore: t.field({ type: 'DateTime' }),
  }),
})

// Review connections for pagination
// Note: Using manual connection implementation

// Sorting options
export const ReviewSortField = builder.enumType('ReviewSortField', {
  values: ['createdAt', 'rating'] as const,
})

export const ReviewOrderByInput = builder.inputType('ReviewOrderByInput', {
  fields: (t) => ({
    field: t.field({ type: ReviewSortField, required: true }),
    direction: t.field({ type: SortDirection, required: true }),
  }),
})
