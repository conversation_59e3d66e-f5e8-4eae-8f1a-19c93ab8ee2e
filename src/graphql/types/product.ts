import { builder } from '../../lib/builder'
import { SortDirection } from './common'

// PriceRange object type - define this first
export const PriceRange = builder.objectRef<{ min: number; max: number }>('PriceRange').implement({
  fields: (t) => ({
    min: t.exposeFloat('min'),
    max: t.exposeFloat('max'),
  }),
})

// Product type
export const Product = builder.prismaObject('Product', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description'),
    createdAt: t.expose('createdAt', { type: 'DateTime' }),
    updatedAt: t.expose('updatedAt', { type: 'DateTime' }),
    
    // Relations
    category: t.relation('category'),
    variants: t.relation('variants'),
    images: t.relation('images'),
    tags: t.relation('tags'),
    reviews: t.relation('reviews'),
    
    // Computed fields
    averageRating: t.float({
      resolve: async (product, args, { prisma }) => {
        const result = await prisma.review.aggregate({
          where: { productId: product.id },
          _avg: { rating: true },
        })
        return result._avg.rating || 0
      },
    }),
    reviewCount: t.int({
      resolve: async (product, args, { prisma }) => {
        return prisma.review.count({
          where: { productId: product.id },
        })
      },
    }),
    priceRange: t.field({
      type: PriceRange,
      resolve: async (product, args, { prisma }) => {
        const result = await prisma.productVariant.aggregate({
          where: { productId: product.id },
          _min: { price: true },
          _max: { price: true },
        })
        return {
          min: result._min.price || 0,
          max: result._max.price || 0,
        }
      },
    }),
  }),
})

// Product Variant type
export const ProductVariant = builder.prismaObject('ProductVariant', {
  fields: (t) => ({
    id: t.exposeID('id'),
    sku: t.exposeString('sku'),
    price: t.exposeFloat('price'),
    quantity: t.exposeInt('quantity'),
    
    // Relations
    product: t.relation('product'),
    size: t.relation('size'),
    color: t.relation('color'),
    discounts: t.relation('discounts'),
    wishlistItems: t.relation('wishlistItems'),
    orderItems: t.relation('orderItems'),
    
    // Computed fields
    discountedPrice: t.float({
      resolve: async (variant, args, { prisma }) => {
        const activeDiscounts = await prisma.variantDiscount.findMany({
          where: {
            productVariantId: variant.id,
            discount: {
              startDate: { lte: new Date() },
              endDate: { gte: new Date() },
            },
          },
          include: { discount: true },
        })
        
        let finalPrice = variant.price
        for (const { discount } of activeDiscounts) {
          if (discount.discountPercent) {
            finalPrice = finalPrice * (1 - discount.discountPercent / 100)
          } else if (discount.discountAmount) {
            finalPrice = Math.max(0, finalPrice - discount.discountAmount)
          }
        }
        return finalPrice
      },
    }),
    isInStock: t.boolean({
      resolve: (variant) => variant.quantity > 0,
    }),
  }),
})

// Product Image type
export const ProductImage = builder.prismaObject('ProductImage', {
  fields: (t) => ({
    id: t.exposeID('id'),
    imageUrl: t.exposeString('imageUrl'),
    isPrimary: t.exposeBoolean('isPrimary'),
    product: t.relation('product'),
  }),
})

// Category type
export const Category = builder.prismaObject('Category', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    products: t.relation('products'),
    
    // Computed fields
    productCount: t.int({
      resolve: async (category, args, { prisma }) => {
        return prisma.product.count({
          where: { categoryId: category.id },
        })
      },
    }),
  }),
})

// Size and Color types
export const Size = builder.prismaObject('Size', {
  fields: (t) => ({
    id: t.exposeID('id'),
    sizeLabel: t.exposeString('sizeLabel'),
    variants: t.relation('variants'),
  }),
})

export const Color = builder.prismaObject('Color', {
  fields: (t) => ({
    id: t.exposeID('id'),
    colorName: t.exposeString('colorName'),
    hexCode: t.exposeString('hexCode', { nullable: true }),
    variants: t.relation('variants'),
  }),
})

// Tag and ProductTag types
export const Tag = builder.prismaObject('Tag', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    productTags: t.relation('productTags'),
  }),
})

export const ProductTag = builder.prismaObject('ProductTag', {
  fields: (t) => ({
    id: t.exposeID('id'),
    product: t.relation('product'),
    tag: t.relation('tag'),
  }),
})

// Discount types
export const Discount = builder.prismaObject('Discount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    name: t.exposeString('name'),
    description: t.exposeString('description', { nullable: true }),
    discountPercent: t.exposeInt('discountPercent', { nullable: true }),
    discountAmount: t.exposeInt('discountAmount', { nullable: true }),
    startDate: t.expose('startDate', { type: 'DateTime' }),
    endDate: t.expose('endDate', { type: 'DateTime' }),
    variants: t.relation('variants'),
    
    // Computed fields
    isActive: t.boolean({
      resolve: (discount) => {
        const now = new Date()
        return discount.startDate <= now && discount.endDate >= now
      },
    }),
  }),
})

export const VariantDiscount = builder.prismaObject('VariantDiscount', {
  fields: (t) => ({
    id: t.exposeID('id'),
    productVariant: t.relation('productVariant'),
    discount: t.relation('discount'),
  }),
})

// Input types for Product operations
export const ProductCreateInput = builder.inputType('ProductCreateInput', {
  fields: (t) => ({
    name: t.string({ required: true, validate: { minLength: 1 } }),
    description: t.string({ required: true, validate: { minLength: 1 } }),
    categoryId: t.string({ required: true }),
  }),
})

export const ProductUpdateInput = builder.inputType('ProductUpdateInput', {
  fields: (t) => ({
    name: t.string({ validate: { minLength: 1 } }),
    description: t.string({ validate: { minLength: 1 } }),
    categoryId: t.string(),
  }),
})

export const ProductFilterInput = builder.inputType('ProductFilterInput', {
  fields: (t) => ({
    name: t.string(),
    categoryId: t.string(),
    minPrice: t.float(),
    maxPrice: t.float(),
    sizeIds: t.stringList(),
    colorIds: t.stringList(),
    tagIds: t.stringList(),
    inStock: t.boolean(),
  }),
})

// Sorting options
export const ProductSortField = builder.enumType('ProductSortField', {
  values: ['name', 'createdAt', 'updatedAt'] as const,
})

export const ProductOrderByInput = builder.inputType('ProductOrderByInput', {
  fields: (t) => ({
    field: t.field({ type: ProductSortField, required: true }),
    direction: t.field({ type: SortDirection, required: true }),
  }),
})
