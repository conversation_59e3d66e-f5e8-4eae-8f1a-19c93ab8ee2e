import { builder } from '../lib/builder'

// Define root Query type
builder.queryType({
  fields: (t) => ({
    // Health check
    health: t.string({
      resolve: () => 'GraphQL API is running!',
    }),

    // Customer queries
    customer: t.prismaField({
      type: 'Customer',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.customer.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    customers: t.prismaField({
      type: ['Customer'],
      args: {
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.customer.findMany({
          ...query,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy: { createdAt: 'desc' },
        })
      },
    }),

    // Product queries
    product: t.prismaField({
      type: 'Product',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        console.log('🔍 Product query - ID received:', JSON.stringify(args.id))
        console.log('🔍 Product query - ID length:', args.id.length)
        console.log('🔍 Product query - Query object:', JSON.stringify(query, null, 2))
        
        try {
          // First try a simple findMany to see if we can find any products
          const allProducts = await ctx.prisma.product.findMany({
            take: 3,
            select: { id: true, name: true }
          })
          console.log('🔍 Available products:', allProducts.map(p => `${p.id}: ${p.name}`))
          
          // Try findFirst with the same ID
          const findFirstResult = await ctx.prisma.product.findFirst({
            where: { id: args.id },
            include: { category: true }
          })
          console.log('🔍 findFirst result:', findFirstResult ? `Found: ${findFirstResult.name}` : 'NULL')
          
          // Now try findUnique
          const result = await ctx.prisma.product.findUnique({
            ...query,
            where: { id: args.id },
          })
          console.log('🔍 findUnique result:', result ? `Found: ${result.name}` : 'NULL')
          
          // If findUnique fails but findFirst works, return findFirst result
          return result || findFirstResult
          
        } catch (error) {
          console.error('🔍 Product query - Error:', error)
          throw error
        }
      },
    }),

    products: t.prismaField({
      type: ['Product'],
      args: {
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
        // Category filters
        categoryId: t.arg.string(),
        categoryName: t.arg.string(),
        // Search filters
        search: t.arg.string(),
        name: t.arg.string(),
        // Price filters
        minPrice: t.arg.float(),
        maxPrice: t.arg.float(),
        // Size and color filters
        sizeIds: t.arg.stringList(),
        colorIds: t.arg.stringList(),
        sizeLabels: t.arg.stringList(),
        colorNames: t.arg.stringList(),
        // Tag filters
        tagIds: t.arg.stringList(),
        tagNames: t.arg.stringList(),
        // Availability filters
        inStock: t.arg.boolean(),
        minQuantity: t.arg.int(),
        // Discount filters
        hasDiscount: t.arg.boolean(),
        discountPercent: t.arg.int(),
        // Date filters
        createdAfter: t.arg.string(),
        createdBefore: t.arg.string(),
        // Sorting options
        sortBy: t.arg.string({ defaultValue: 'createdAt' }),
        sortOrder: t.arg.string({ defaultValue: 'desc' }),
      },
      resolve: async (query, root, args, ctx) => {
        const where: any = {}
        
        // Category filters
        if (args.categoryId) {
          where.categoryId = args.categoryId
        }
        if (args.categoryName) {
          where.category = {
            name: { contains: args.categoryName, mode: 'insensitive' }
          }
        }
        
        // Search filters
        if (args.search) {
          where.OR = [
            { name: { contains: args.search, mode: 'insensitive' } },
            { description: { contains: args.search, mode: 'insensitive' } },
            {
              tags: {
                some: {
                  tag: {
                    name: { contains: args.search, mode: 'insensitive' }
                  }
                }
              }
            }
          ]
        }
        if (args.name) {
          where.name = { contains: args.name, mode: 'insensitive' }
        }
        
        // Tag filters
        if (args.tagIds && args.tagIds.length > 0) {
          where.tags = {
            some: {
              tagId: { in: args.tagIds }
            }
          }
        }
        if (args.tagNames && args.tagNames.length > 0) {
          where.tags = {
            some: {
              tag: {
                name: { in: args.tagNames, mode: 'insensitive' }
              }
            }
          }
        }
        
        // Date filters
        if (args.createdAfter || args.createdBefore) {
          where.createdAt = {}
          if (args.createdAfter) {
            where.createdAt.gte = new Date(args.createdAfter)
          }
          if (args.createdBefore) {
            where.createdAt.lte = new Date(args.createdBefore)
          }
        }
        
        // Variant-based filters (price, size, color, stock, discount)
        const variantFilters: any = {}
        
        // Price filters
        if (args.minPrice !== undefined || args.maxPrice !== undefined) {
          variantFilters.price = {}
          if (args.minPrice !== undefined) variantFilters.price.gte = args.minPrice
          if (args.maxPrice !== undefined) variantFilters.price.lte = args.maxPrice
        }
        
        // Size filters
        if (args.sizeIds && args.sizeIds.length > 0) {
          variantFilters.sizeId = { in: args.sizeIds }
        }
        if (args.sizeLabels && args.sizeLabels.length > 0) {
          variantFilters.size = {
            sizeLabel: { in: args.sizeLabels }
          }
        }
        
        // Color filters
        if (args.colorIds && args.colorIds.length > 0) {
          variantFilters.colorId = { in: args.colorIds }
        }
        if (args.colorNames && args.colorNames.length > 0) {
          variantFilters.color = {
            colorName: { in: args.colorNames, mode: 'insensitive' }
          }
        }
        
        // Stock filters
        if (args.inStock !== undefined) {
          if (args.inStock) {
            variantFilters.quantity = { gt: 0 }
          } else {
            variantFilters.quantity = { lte: 0 }
          }
        }
        if (args.minQuantity !== undefined) {
          variantFilters.quantity = { 
            ...variantFilters.quantity,
            gte: args.minQuantity 
          }
        }
        
        // Discount filters
        if (args.hasDiscount !== undefined) {
          if (args.hasDiscount) {
            variantFilters.discounts = {
              some: {
                discount: {
                  startDate: { lte: new Date() },
                  endDate: { gte: new Date() }
                }
              }
            }
          } else {
            variantFilters.discounts = { none: {} }
          }
        }
        if (args.discountPercent !== undefined) {
          variantFilters.discounts = {
            some: {
              discount: {
                discountPercent: { gte: args.discountPercent },
                startDate: { lte: new Date() },
                endDate: { gte: new Date() }
              }
            }
          }
        }
        
        // Apply variant filters if any exist
        if (Object.keys(variantFilters).length > 0) {
          where.variants = { some: variantFilters }
        }
        
        // Sorting
        let orderBy: any = { createdAt: 'desc' }
        if (args.sortBy) {
          const sortOrder = args.sortOrder === 'asc' ? 'asc' : 'desc'
          
          switch (args.sortBy) {
            case 'name':
              orderBy = { name: sortOrder }
              break
            case 'price':
              // For price sorting, we need to sort by the minimum variant price
              orderBy = {
                variants: {
                  _min: { price: sortOrder }
                }
              }
              break
            case 'createdAt':
              orderBy = { createdAt: sortOrder }
              break
            case 'updatedAt':
              orderBy = { updatedAt: sortOrder }
              break
            default:
              orderBy = { createdAt: 'desc' }
          }
        }
        
        return ctx.prisma.product.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy,
        })
      },
    }),

    // Advanced product search with aggregated filters
    searchProducts: t.prismaField({
      type: ['Product'],
      args: {
        // Basic search
        query: t.arg.string(),
        // Pagination
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
        // Filters object-style
        filters: t.arg.string(), // JSON string with filter options
        // Quick filters
        categories: t.arg.stringList(),
        priceRange: t.arg.stringList(), // ["min", "max"]
        sizes: t.arg.stringList(),
        colors: t.arg.stringList(),
        tags: t.arg.stringList(),
        inStockOnly: t.arg.boolean({ defaultValue: false }),
        onSaleOnly: t.arg.boolean({ defaultValue: false }),
        // Sorting
        sortBy: t.arg.string({ defaultValue: 'relevance' }),
        sortOrder: t.arg.string({ defaultValue: 'desc' }),
      },
      resolve: async (query, root, args, ctx) => {
        const where: any = {}
        let orderBy: any = { createdAt: 'desc' }
        
        // Parse filters if provided
        let parsedFilters: any = {}
        if (args.filters) {
          try {
            parsedFilters = JSON.parse(args.filters)
          } catch (e) {
            // Invalid JSON, ignore
          }
        }
        
        // Text search
        if (args.query) {
          where.OR = [
            { name: { contains: args.query, mode: 'insensitive' } },
            { description: { contains: args.query, mode: 'insensitive' } },
            {
              tags: {
                some: {
                  tag: {
                    name: { contains: args.query, mode: 'insensitive' }
                  }
                }
              }
            },
            {
              category: {
                name: { contains: args.query, mode: 'insensitive' }
              }
            }
          ]
        }
        
        // Category filter
        if (args.categories && args.categories.length > 0) {
          where.category = {
            name: { in: args.categories, mode: 'insensitive' }
          }
        }
        
        // Tag filter
        if (args.tags && args.tags.length > 0) {
          where.tags = {
            some: {
              tag: {
                name: { in: args.tags, mode: 'insensitive' }
              }
            }
          }
        }
        
        // Variant-based filters
        const variantFilters: any = {}
        
        // Price range filter
        if (args.priceRange && args.priceRange.length === 2) {
          const minPrice = parseFloat(args.priceRange[0])
          const maxPrice = parseFloat(args.priceRange[1])
          if (!isNaN(minPrice) && !isNaN(maxPrice)) {
            variantFilters.price = { gte: minPrice, lte: maxPrice }
          }
        }
        
        // Size filter
        if (args.sizes && args.sizes.length > 0) {
          variantFilters.size = {
            sizeLabel: { in: args.sizes }
          }
        }
        
        // Color filter
        if (args.colors && args.colors.length > 0) {
          variantFilters.color = {
            colorName: { in: args.colors, mode: 'insensitive' }
          }
        }
        
        // Stock filter
        if (args.inStockOnly) {
          variantFilters.quantity = { gt: 0 }
        }
        
        // Sale filter
        if (args.onSaleOnly) {
          variantFilters.discounts = {
            some: {
              discount: {
                startDate: { lte: new Date() },
                endDate: { gte: new Date() }
              }
            }
          }
        }
        
        // Apply variant filters
        if (Object.keys(variantFilters).length > 0) {
          where.variants = { some: variantFilters }
        }
        
        // Sorting logic
        switch (args.sortBy) {
          case 'name':
            orderBy = { name: args.sortOrder === 'desc' ? 'desc' : 'asc' }
            break
          case 'price':
            orderBy = {
              variants: {
                _min: { price: args.sortOrder === 'desc' ? 'desc' : 'asc' }
              }
            }
            break
          case 'newest':
            orderBy = { createdAt: 'desc' }
            break
          case 'oldest':
            orderBy = { createdAt: 'asc' }
            break
          case 'relevance':
          default:
            // For relevance, prioritize products with query matches
            if (args.query) {
              orderBy = [
                { name: 'asc' }, // Products with matching names first
                { createdAt: 'desc' }
              ]
            } else {
              orderBy = { createdAt: 'desc' }
            }
            break
        }
        
        return ctx.prisma.product.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy,
        })
      },
    }),

    // Category queries
    category: t.prismaField({
      type: 'Category',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.category.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    categories: t.prismaField({
      type: ['Category'],
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.category.findMany({
          ...query,
          orderBy: { name: 'asc' },
        })
      },
    }),

    // Size queries
    size: t.prismaField({
      type: 'Size',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.size.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    sizes: t.prismaField({
      type: ['Size'],
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.size.findMany({
          ...query,
          orderBy: { sizeLabel: 'asc' },
        })
      },
    }),

    // Color queries
    color: t.prismaField({
      type: 'Color',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.color.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    colors: t.prismaField({
      type: ['Color'],
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.color.findMany({
          ...query,
          orderBy: { colorName: 'asc' },
        })
      },
    }),

    // Tag queries
    tag: t.prismaField({
      type: 'Tag',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.tag.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    tags: t.prismaField({
      type: ['Tag'],
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.tag.findMany({
          ...query,
          orderBy: { name: 'asc' },
        })
      },
    }),

    // Wishlist queries
    wishlist: t.prismaField({
      type: 'Wishlist',
      args: {
        customerId: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.wishlist.findUnique({
          ...query,
          where: { customerId: args.customerId },
        })
      },
    }),

    // Review queries
    review: t.prismaField({
      type: 'Review',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.review.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    reviews: t.prismaField({
      type: ['Review'],
      args: {
        productId: t.arg.string(),
        customerId: t.arg.string(),
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
      },
      resolve: async (query, root, args, ctx) => {
        const where: Record<string, unknown> = {}
        if (args.productId) where.productId = args.productId
        if (args.customerId) where.customerId = args.customerId

        return ctx.prisma.review.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy: { createdAt: 'desc' },
        })
      },
    }),

    // PromoCode queries
    promoCode: t.prismaField({
      type: 'PromoCode',
      args: {
        code: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.promoCode.findUnique({
          ...query,
          where: { code: args.code },
        })
      },
    }),

    promoCodes: t.prismaField({
      type: ['PromoCode'],
      args: {
        isActive: t.arg.boolean(),
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
      },
      resolve: async (query, root, args, ctx) => {
        const where: Record<string, unknown> = {}
        if (args.isActive !== null && args.isActive !== undefined) {
          where.isActive = args.isActive
        }
        return ctx.prisma.promoCode.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy: { expiration: 'desc' },
        })
      },
    }),

    // Order queries
    order: t.prismaField({
      type: 'Order',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.order.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    orders: t.prismaField({
      type: ['Order'],
      args: {
        customerId: t.arg.string(),
        status: t.arg.string(),
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
      },
      resolve: async (query, root, args, ctx) => {
        const where: Record<string, unknown> = {}
        if (args.customerId) where.customerId = args.customerId
        if (args.status) where.status = args.status

        return ctx.prisma.order.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy: { orderDate: 'desc' },
        })
      },
    }),

    // ProductVariant queries
    productVariant: t.prismaField({
      type: 'ProductVariant',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.productVariant.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    productVariants: t.prismaField({
      type: ['ProductVariant'],
      args: {
        productId: t.arg.string(),
        sizeId: t.arg.string(),
        colorId: t.arg.string(),
        sizeIds: t.arg.stringList(),
        colorIds: t.arg.stringList(),
        sizeLabels: t.arg.stringList(),
        colorNames: t.arg.stringList(),
        // Price filters
        minPrice: t.arg.float(),
        maxPrice: t.arg.float(),
        // Stock filters
        inStock: t.arg.boolean(),
        minQuantity: t.arg.int(),
        maxQuantity: t.arg.int(),
        // SKU filter
        sku: t.arg.string(),
        // Discount filters
        hasDiscount: t.arg.boolean(),
        discountPercent: t.arg.int(),
        // Sorting
        sortBy: t.arg.string({ defaultValue: 'price' }),
        sortOrder: t.arg.string({ defaultValue: 'asc' }),
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
      },
      resolve: async (query, root, args, ctx) => {
        const where: any = {}
        
        // Basic filters
        if (args.productId) where.productId = args.productId
        if (args.sizeId) where.sizeId = args.sizeId
        if (args.colorId) where.colorId = args.colorId
        if (args.sku) where.sku = { contains: args.sku, mode: 'insensitive' }
        
        // Multiple size filters
        if (args.sizeIds && args.sizeIds.length > 0) {
          where.sizeId = { in: args.sizeIds }
        }
        if (args.sizeLabels && args.sizeLabels.length > 0) {
          where.size = {
            sizeLabel: { in: args.sizeLabels }
          }
        }
        
        // Multiple color filters
        if (args.colorIds && args.colorIds.length > 0) {
          where.colorId = { in: args.colorIds }
        }
        if (args.colorNames && args.colorNames.length > 0) {
          where.color = {
            colorName: { in: args.colorNames, mode: 'insensitive' }
          }
        }
        
        // Price filters
        if (args.minPrice !== undefined || args.maxPrice !== undefined) {
          where.price = {}
          if (args.minPrice !== undefined) where.price.gte = args.minPrice
          if (args.maxPrice !== undefined) where.price.lte = args.maxPrice
        }
        
        // Stock filters
        if (args.inStock !== undefined) {
          if (args.inStock) {
            where.quantity = { gt: 0 }
          } else {
            where.quantity = { lte: 0 }
          }
        }
        if (args.minQuantity !== undefined) {
          where.quantity = { 
            ...where.quantity,
            gte: args.minQuantity 
          }
        }
        if (args.maxQuantity !== undefined) {
          where.quantity = { 
            ...where.quantity,
            lte: args.maxQuantity 
          }
        }
        
        // Discount filters
        if (args.hasDiscount !== undefined) {
          if (args.hasDiscount) {
            where.discounts = {
              some: {
                discount: {
                  startDate: { lte: new Date() },
                  endDate: { gte: new Date() }
                }
              }
            }
          } else {
            where.discounts = { none: {} }
          }
        }
        if (args.discountPercent !== undefined) {
          where.discounts = {
            some: {
              discount: {
                discountPercent: { gte: args.discountPercent },
                startDate: { lte: new Date() },
                endDate: { gte: new Date() }
              }
            }
          }
        }
        
        // Sorting
        let orderBy: any = { price: 'asc' }
        if (args.sortBy) {
          const sortOrder = args.sortOrder === 'desc' ? 'desc' : 'asc'
          
          switch (args.sortBy) {
            case 'price':
              orderBy = { price: sortOrder }
              break
            case 'quantity':
              orderBy = { quantity: sortOrder }
              break
            case 'sku':
              orderBy = { sku: sortOrder }
              break
            case 'size':
              orderBy = { size: { sizeLabel: sortOrder } }
              break
            case 'color':
              orderBy = { color: { colorName: sortOrder } }
              break
            default:
              orderBy = { price: 'asc' }
          }
        }

        return ctx.prisma.productVariant.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy,
        })
      },
    }),

    // Discount queries
    discount: t.prismaField({
      type: 'Discount',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.discount.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    discounts: t.prismaField({
      type: ['Discount'],
      args: {
        active: t.arg.boolean(),
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
      },
      resolve: async (query, root, args, ctx) => {
        const now = new Date()
        const where: Record<string, unknown> = {}
        
        if (args.active) {
          where.startDate = { lte: now }
          where.endDate = { gte: now }
        }

        return ctx.prisma.discount.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy: { startDate: 'desc' },
        })
      },
    }),

    // PaymentMethod queries
    paymentMethod: t.prismaField({
      type: 'PaymentMethod',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.paymentMethod.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    paymentMethods: t.prismaField({
      type: ['PaymentMethod'],
      args: {
        customerId: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.paymentMethod.findMany({
          ...query,
          where: { customerId: args.customerId },
          orderBy: { createdAt: 'desc' },
        })
      },
    }),

    // PaymentTransaction queries
    paymentTransaction: t.prismaField({
      type: 'PaymentTransaction',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.paymentTransaction.findUnique({
          ...query,
          where: { id: args.id },
        })
      },
    }),

    paymentTransactions: t.prismaField({
      type: ['PaymentTransaction'],
      args: {
        orderId: t.arg.string(),
        status: t.arg.string(),
        take: t.arg.int({ defaultValue: 20 }),
        skip: t.arg.int({ defaultValue: 0 }),
      },
      resolve: async (query, root, args, ctx) => {
        const where: Record<string, unknown> = {}
        if (args.orderId) where.orderId = args.orderId
        if (args.status) where.status = args.status

        return ctx.prisma.paymentTransaction.findMany({
          ...query,
          where,
          take: args.take || 20,
          skip: args.skip || 0,
          orderBy: { createdAt: 'desc' },
        })
      },
    }),

    // ProductImage queries
    productImages: t.prismaField({
      type: ['ProductImage'],
      args: {
        productId: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.productImage.findMany({
          ...query,
          where: { productId: args.productId },
          orderBy: { isPrimary: 'desc' },
        })
      },
    }),

    // Filter options for product search
    productFilterOptions: t.field({
      type: 'String', // Return as JSON string
      args: {
        categoryId: t.arg.string(),
      },
      resolve: async (root, args, ctx) => {
        // Get available filter options based on existing products
        const productWhere = args.categoryId ? { categoryId: args.categoryId } : {}
        
        const [categories, sizes, colors, tags, priceRange] = await Promise.all([
          // Categories
          ctx.prisma.category.findMany({
            select: { id: true, name: true, _count: { select: { products: true } } },
            orderBy: { name: 'asc' },
          }),
          
          // Sizes (from variants of existing products)
          ctx.prisma.size.findMany({
            where: {
              variants: {
                some: {
                  product: productWhere
                }
              }
            },
            select: { id: true, sizeLabel: true },
            orderBy: { sizeLabel: 'asc' },
          }),
          
          // Colors (from variants of existing products)
          ctx.prisma.color.findMany({
            where: {
              variants: {
                some: {
                  product: productWhere
                }
              }
            },
            select: { id: true, colorName: true, hexCode: true },
            orderBy: { colorName: 'asc' },
          }),
          
          // Tags (from products)
          ctx.prisma.tag.findMany({
            where: {
              productTags: {
                some: {
                  product: productWhere
                }
              }
            },
            select: { id: true, name: true },
            orderBy: { name: 'asc' },
          }),
          
          // Price range
          ctx.prisma.productVariant.aggregate({
            where: {
              product: productWhere
            },
            _min: { price: true },
            _max: { price: true },
          }),
        ])

        const filterOptions = {
          categories: categories.map(cat => ({
            id: cat.id,
            name: cat.name,
            productCount: cat._count.products
          })),
          sizes: sizes.map(size => ({
            id: size.id,
            label: size.sizeLabel
          })),
          colors: colors.map(color => ({
            id: color.id,
            name: color.colorName,
            hex: color.hexCode
          })),
          tags: tags.map(tag => ({
            id: tag.id,
            name: tag.name
          })),
          priceRange: {
            min: priceRange._min.price || 0,
            max: priceRange._max.price || 0
          },
          sortOptions: [
            { value: 'relevance', label: 'Relevance' },
            { value: 'name', label: 'Name' },
            { value: 'price', label: 'Price' },
            { value: 'newest', label: 'Newest First' },
            { value: 'oldest', label: 'Oldest First' }
          ]
        }

        return JSON.stringify(filterOptions)
      },
    }),
  }),
})
