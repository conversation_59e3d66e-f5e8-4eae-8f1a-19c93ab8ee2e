import { builder } from '../lib/builder'

// Define root Mutation type
builder.mutationType({
  fields: (t) => ({
    // Customer mutations
    createCustomer: t.prismaField({
      type: 'Customer',
      args: {
        email: t.arg.string({ required: true }),
        firstName: t.arg.string({ required: true }),
        lastName: t.arg.string({ required: true }),
        phone: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        // Check if email already exists
        const existingCustomer = await ctx.prisma.customer.findUnique({
          where: { email: args.email },
        })
        
        if (existingCustomer) {
          throw new Error('Customer with this email already exists')
        }

        // Create customer and their wishlist
        const customer = await ctx.prisma.customer.create({
          ...query,
          data: {
            email: args.email,
            firstName: args.firstName,
            lastName: args.lastName,
            phone: args.phone,
            wishlist: {
              create: {},
            },
          },
        })

        return customer
      },
    }),

    updateCustomer: t.prisma<PERSON>ield({
      type: 'Customer',
      args: {
        id: t.arg.string({ required: true }),
        email: t.arg.string(),
        firstName: t.arg.string(),
        lastName: t.arg.string(),
        phone: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        // Check if customer exists
        const existingCustomer = await ctx.prisma.customer.findUnique({
          where: { id: args.id },
        })
        
        if (!existingCustomer) {
          throw new Error('Customer not found')
        }

        // Check if email is being updated and if it's already taken
        if (args.email && args.email !== existingCustomer.email) {
          const emailTaken = await ctx.prisma.customer.findUnique({
            where: { email: args.email },
          })
          
          if (emailTaken) {
            throw new Error('Email already in use by another customer')
          }
        }

        const updateData: Record<string, unknown> = {}
        if (args.email) updateData.email = args.email
        if (args.firstName) updateData.firstName = args.firstName
        if (args.lastName) updateData.lastName = args.lastName
        if (args.phone !== undefined) updateData.phone = args.phone

        return ctx.prisma.customer.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deleteCustomer: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.customer.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete customer')
        }
      },
    }),

    // Product mutations
    createProduct: t.prismaField({
      type: 'Product',
      args: {
        name: t.arg.string({ required: true }),
        description: t.arg.string({ required: true }),
        categoryId: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        // Verify category exists
        const category = await ctx.prisma.category.findUnique({
          where: { id: args.categoryId },
        })
        
        if (!category) {
          throw new Error('Category not found')
        }

        return ctx.prisma.product.create({
          ...query,
          data: {
            name: args.name,
            description: args.description,
            categoryId: args.categoryId,
          },
        })
      },
    }),

    updateProduct: t.prismaField({
      type: 'Product',
      args: {
        id: t.arg.string({ required: true }),
        name: t.arg.string(),
        description: t.arg.string(),
        categoryId: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        // Check if product exists
        const existingProduct = await ctx.prisma.product.findUnique({
          where: { id: args.id },
        })
        
        if (!existingProduct) {
          throw new Error('Product not found')
        }

        // Verify category exists if being updated
        if (args.categoryId) {
          const category = await ctx.prisma.category.findUnique({
            where: { id: args.categoryId },
          })
          
          if (!category) {
            throw new Error('Category not found')
          }
        }

        const updateData: Record<string, unknown> = {}
        if (args.name) updateData.name = args.name
        if (args.description) updateData.description = args.description
        if (args.categoryId) updateData.categoryId = args.categoryId

        return ctx.prisma.product.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deleteProduct: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.product.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete product')
        }
      },
    }),

    // Category mutations
    createCategory: t.prismaField({
      type: 'Category',
      args: {
        name: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.category.create({
          ...query,
          data: {
            name: args.name,
          },
        })
      },
    }),

    updateCategory: t.prismaField({
      type: 'Category',
      args: {
        id: t.arg.string({ required: true }),
        name: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.category.update({
          ...query,
          where: { id: args.id },
          data: { name: args.name },
        })
      },
    }),

    deleteCategory: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.category.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete category')
        }
      },
    }),

    // Size mutations
    createSize: t.prismaField({
      type: 'Size',
      args: {
        sizeLabel: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.size.create({
          ...query,
          data: {
            sizeLabel: args.sizeLabel,
          },
        })
      },
    }),

    updateSize: t.prismaField({
      type: 'Size',
      args: {
        id: t.arg.string({ required: true }),
        sizeLabel: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.size.update({
          ...query,
          where: { id: args.id },
          data: { sizeLabel: args.sizeLabel },
        })
      },
    }),

    deleteSize: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.size.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete size')
        }
      },
    }),

    // Color mutations
    createColor: t.prismaField({
      type: 'Color',
      args: {
        colorName: t.arg.string({ required: true }),
        hexCode: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.color.create({
          ...query,
          data: {
            colorName: args.colorName,
            hexCode: args.hexCode,
          },
        })
      },
    }),

    updateColor: t.prismaField({
      type: 'Color',
      args: {
        id: t.arg.string({ required: true }),
        colorName: t.arg.string(),
        hexCode: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        const updateData: Record<string, unknown> = {}
        if (args.colorName) updateData.colorName = args.colorName
        if (args.hexCode !== undefined) updateData.hexCode = args.hexCode

        return ctx.prisma.color.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deleteColor: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.color.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete color')
        }
      },
    }),

    // Tag mutations
    createTag: t.prismaField({
      type: 'Tag',
      args: {
        name: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.tag.create({
          ...query,
          data: {
            name: args.name,
          },
        })
      },
    }),

    updateTag: t.prismaField({
      type: 'Tag',
      args: {
        id: t.arg.string({ required: true }),
        name: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.tag.update({
          ...query,
          where: { id: args.id },
          data: { name: args.name },
        })
      },
    }),

    deleteTag: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.tag.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete tag')
        }
      },
    }),

    // ProductVariant mutations
    createProductVariant: t.prismaField({
      type: 'ProductVariant',
      args: {
        productId: t.arg.string({ required: true }),
        sizeId: t.arg.string({ required: true }),
        colorId: t.arg.string({ required: true }),
        sku: t.arg.string({ required: true }),
        price: t.arg.float({ required: true }),
        quantity: t.arg.int({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        // Verify product, size, and color exist
        const [product, size, color] = await Promise.all([
          ctx.prisma.product.findUnique({ where: { id: args.productId } }),
          ctx.prisma.size.findUnique({ where: { id: args.sizeId } }),
          ctx.prisma.color.findUnique({ where: { id: args.colorId } }),
        ])

        if (!product) throw new Error('Product not found')
        if (!size) throw new Error('Size not found')
        if (!color) throw new Error('Color not found')

        return ctx.prisma.productVariant.create({
          ...query,
          data: {
            productId: args.productId,
            sizeId: args.sizeId,
            colorId: args.colorId,
            sku: args.sku,
            price: args.price,
            quantity: args.quantity,
          },
        })
      },
    }),

    updateProductVariant: t.prismaField({
      type: 'ProductVariant',
      args: {
        id: t.arg.string({ required: true }),
        sku: t.arg.string(),
        price: t.arg.float(),
        quantity: t.arg.int(),
      },
      resolve: async (query, root, args, ctx) => {
        const updateData: Record<string, unknown> = {}
        if (args.sku) updateData.sku = args.sku
        if (args.price !== undefined) updateData.price = args.price
        if (args.quantity !== undefined) updateData.quantity = args.quantity

        return ctx.prisma.productVariant.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deleteProductVariant: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.productVariant.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete product variant')
        }
      },
    }),

    // Wishlist mutations
    addToWishlist: t.prismaField({
      type: 'WishlistItem',
      args: {
        customerId: t.arg.string({ required: true }),
        productVariantId: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        // Get or create wishlist for customer
        let wishlist = await ctx.prisma.wishlist.findUnique({
          where: { customerId: args.customerId },
        })

        if (!wishlist) {
          wishlist = await ctx.prisma.wishlist.create({
            data: { customerId: args.customerId },
          })
        }

        // Check if item already exists in wishlist
        const existingItem = await ctx.prisma.wishlistItem.findFirst({
          where: {
            wishlistId: wishlist.id,
            productVariantId: args.productVariantId,
          },
        })

        if (existingItem) {
          throw new Error('Item already in wishlist')
        }

        return ctx.prisma.wishlistItem.create({
          ...query,
          data: {
            wishlistId: wishlist.id,
            productVariantId: args.productVariantId,
          },
        })
      },
    }),

    removeFromWishlist: t.field({
      type: 'Boolean',
      args: {
        customerId: t.arg.string({ required: true }),
        productVariantId: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        const wishlist = await ctx.prisma.wishlist.findUnique({
          where: { customerId: args.customerId },
        })

        if (!wishlist) {
          throw new Error('Wishlist not found')
        }

        try {
          await ctx.prisma.wishlistItem.deleteMany({
            where: {
              wishlistId: wishlist.id,
              productVariantId: args.productVariantId,
            },
          })
          return true
        } catch {
          throw new Error('Failed to remove item from wishlist')
        }
      },
    }),

    // Review mutations
    createReview: t.prismaField({
      type: 'Review',
      args: {
        customerId: t.arg.string({ required: true }),
        productId: t.arg.string({ required: true }),
        rating: t.arg.int({ required: true }),
        comment: t.arg.string(),
        images: t.arg.stringList(),
      },
      resolve: async (query, root, args, ctx) => {
        if (args.rating < 1 || args.rating > 5) {
          throw new Error('Rating must be between 1 and 5')
        }

        return ctx.prisma.review.create({
          ...query,
          data: {
            customerId: args.customerId,
            productId: args.productId,
            rating: args.rating,
            comment: args.comment,
            images: args.images || [],
          },
        })
      },
    }),

    updateReview: t.prismaField({
      type: 'Review',
      args: {
        id: t.arg.string({ required: true }),
        rating: t.arg.int(),
        comment: t.arg.string(),
        images: t.arg.stringList(),
      },
      resolve: async (query, root, args, ctx) => {
        if (args.rating && (args.rating < 1 || args.rating > 5)) {
          throw new Error('Rating must be between 1 and 5')
        }

        const updateData: Record<string, unknown> = {}
        if (args.rating) updateData.rating = args.rating
        if (args.comment !== undefined) updateData.comment = args.comment
        if (args.images) updateData.images = args.images

        return ctx.prisma.review.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deleteReview: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.review.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete review')
        }
      },
    }),

    // PromoCode mutations
    createPromoCode: t.prismaField({
      type: 'PromoCode',
      args: {
        code: t.arg.string({ required: true }),
        discountType: t.arg.string({ required: true }),
        discountValue: t.arg.float({ required: true }),
        expiration: t.arg.string({ required: true }),
        usageLimit: t.arg.int(),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.promoCode.create({
          ...query,
          data: {
            code: args.code,
            discountType: args.discountType,
            discountValue: args.discountValue,
            expiration: new Date(args.expiration),
            usageLimit: args.usageLimit,
          },
        })
      },
    }),

    updatePromoCode: t.prismaField({
      type: 'PromoCode',
      args: {
        id: t.arg.string({ required: true }),
        discountType: t.arg.string(),
        discountValue: t.arg.float(),
        expiration: t.arg.string(),
        usageLimit: t.arg.int(),
        isActive: t.arg.boolean(),
      },
      resolve: async (query, root, args, ctx) => {
        const updateData: Record<string, unknown> = {}
        if (args.discountType) updateData.discountType = args.discountType
        if (args.discountValue !== undefined) updateData.discountValue = args.discountValue
        if (args.expiration) updateData.expiration = new Date(args.expiration)
        if (args.usageLimit !== undefined) updateData.usageLimit = args.usageLimit
        if (args.isActive !== undefined) updateData.isActive = args.isActive

        return ctx.prisma.promoCode.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deletePromoCode: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.promoCode.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete promo code')
        }
      },
    }),

    // Order mutations
    createOrder: t.prismaField({
      type: 'Order',
      args: {
        customerId: t.arg.string({ required: true }),
        items: t.arg.stringList({ required: true }), // JSON string representation of order items
      },
      resolve: async (query, root, args, ctx) => {
        const items = JSON.parse(args.items[0]) as Array<{
          productVariantId: string
          quantity: number
          priceAtPurchase: number
        }>

        // Calculate total amount
        const totalAmount = items.reduce(
          (sum, item) => sum + item.quantity * item.priceAtPurchase,
          0
        )

        return ctx.prisma.order.create({
          ...query,
          data: {
            customerId: args.customerId,
            totalAmount,
            items: {
              create: items.map((item) => ({
                productVariantId: item.productVariantId,
                quantity: item.quantity,
                priceAtPurchase: item.priceAtPurchase,
              })),
            },
          },
        })
      },
    }),

    updateOrderStatus: t.prismaField({
      type: 'Order',
      args: {
        id: t.arg.string({ required: true }),
        status: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.order.update({
          ...query,
          where: { id: args.id },
          data: { status: args.status as 'pending' | 'shipped' | 'delivered' | 'cancelled' },
        })
      },
    }),

    // Discount mutations
    createDiscount: t.prismaField({
      type: 'Discount',
      args: {
        name: t.arg.string({ required: true }),
        description: t.arg.string(),
        discountPercent: t.arg.int(),
        discountAmount: t.arg.int(),
        startDate: t.arg.string({ required: true }),
        endDate: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.discount.create({
          ...query,
          data: {
            name: args.name,
            description: args.description,
            discountPercent: args.discountPercent,
            discountAmount: args.discountAmount,
            startDate: new Date(args.startDate),
            endDate: new Date(args.endDate),
          },
        })
      },
    }),

    updateDiscount: t.prismaField({
      type: 'Discount',
      args: {
        id: t.arg.string({ required: true }),
        name: t.arg.string(),
        description: t.arg.string(),
        discountPercent: t.arg.int(),
        discountAmount: t.arg.int(),
        startDate: t.arg.string(),
        endDate: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        const updateData: Record<string, unknown> = {}
        if (args.name) updateData.name = args.name
        if (args.description !== undefined) updateData.description = args.description
        if (args.discountPercent !== undefined) updateData.discountPercent = args.discountPercent
        if (args.discountAmount !== undefined) updateData.discountAmount = args.discountAmount
        if (args.startDate) updateData.startDate = new Date(args.startDate)
        if (args.endDate) updateData.endDate = new Date(args.endDate)

        return ctx.prisma.discount.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deleteDiscount: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.discount.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete discount')
        }
      },
    }),

    // PaymentMethod mutations
    createPaymentMethod: t.prismaField({
      type: 'PaymentMethod',
      args: {
        customerId: t.arg.string({ required: true }),
        provider: t.arg.string({ required: true }),
        type: t.arg.string({ required: true }),
        last4: t.arg.string({ required: true }),
        isDefault: t.arg.boolean(),
        metadata: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        // If this is being set as default, unset other defaults
        if (args.isDefault) {
          await ctx.prisma.paymentMethod.updateMany({
            where: { customerId: args.customerId },
            data: { isDefault: false },
          })
        }

        return ctx.prisma.paymentMethod.create({
          ...query,
          data: {
            customerId: args.customerId,
            provider: args.provider,
            type: args.type,
            last4: args.last4,
            isDefault: args.isDefault || false,
            metadata: args.metadata ? JSON.parse(args.metadata) : null,
          },
        })
      },
    }),

    updatePaymentMethod: t.prismaField({
      type: 'PaymentMethod',
      args: {
        id: t.arg.string({ required: true }),
        isDefault: t.arg.boolean(),
        metadata: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        const paymentMethod = await ctx.prisma.paymentMethod.findUnique({
          where: { id: args.id },
        })

        if (!paymentMethod) {
          throw new Error('Payment method not found')
        }

        // If this is being set as default, unset other defaults
        if (args.isDefault) {
          await ctx.prisma.paymentMethod.updateMany({
            where: { 
              customerId: paymentMethod.customerId,
              id: { not: args.id },
            },
            data: { isDefault: false },
          })
        }

        const updateData: Record<string, unknown> = {}
        if (args.isDefault !== undefined) updateData.isDefault = args.isDefault
        if (args.metadata !== undefined) updateData.metadata = args.metadata ? JSON.parse(args.metadata) : null

        return ctx.prisma.paymentMethod.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deletePaymentMethod: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.paymentMethod.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete payment method')
        }
      },
    }),

    // PaymentTransaction mutations
    createPaymentTransaction: t.prismaField({
      type: 'PaymentTransaction',
      args: {
        paymentMethodId: t.arg.string({ required: true }),
        orderId: t.arg.string(),
        amount: t.arg.float({ required: true }),
        status: t.arg.string({ required: true }),
        transactionId: t.arg.string({ required: true }),
        providerResponse: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        return ctx.prisma.paymentTransaction.create({
          ...query,
          data: {
            paymentMethodId: args.paymentMethodId,
            orderId: args.orderId,
            amount: args.amount,
            status: args.status,
            transactionId: args.transactionId,
            providerResponse: args.providerResponse ? JSON.parse(args.providerResponse) : null,
          },
        })
      },
    }),

    updatePaymentTransactionStatus: t.prismaField({
      type: 'PaymentTransaction',
      args: {
        id: t.arg.string({ required: true }),
        status: t.arg.string({ required: true }),
        providerResponse: t.arg.string(),
      },
      resolve: async (query, root, args, ctx) => {
        const updateData: Record<string, unknown> = { status: args.status }
        if (args.providerResponse) {
          updateData.providerResponse = JSON.parse(args.providerResponse)
        }

        return ctx.prisma.paymentTransaction.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    // ProductImage mutations
    createProductImage: t.prismaField({
      type: 'ProductImage',
      args: {
        productId: t.arg.string({ required: true }),
        imageUrl: t.arg.string({ required: true }),
        isPrimary: t.arg.boolean(),
      },
      resolve: async (query, root, args, ctx) => {
        // If this is being set as primary, unset other primary images for this product
        if (args.isPrimary) {
          await ctx.prisma.productImage.updateMany({
            where: { productId: args.productId },
            data: { isPrimary: false },
          })
        }

        return ctx.prisma.productImage.create({
          ...query,
          data: {
            productId: args.productId,
            imageUrl: args.imageUrl,
            isPrimary: args.isPrimary || false,
          },
        })
      },
    }),

    updateProductImage: t.prismaField({
      type: 'ProductImage',
      args: {
        id: t.arg.string({ required: true }),
        imageUrl: t.arg.string(),
        isPrimary: t.arg.boolean(),
      },
      resolve: async (query, root, args, ctx) => {
        const productImage = await ctx.prisma.productImage.findUnique({
          where: { id: args.id },
        })

        if (!productImage) {
          throw new Error('Product image not found')
        }

        // If this is being set as primary, unset other primary images for this product
        if (args.isPrimary) {
          await ctx.prisma.productImage.updateMany({
            where: { 
              productId: productImage.productId,
              id: { not: args.id },
            },
            data: { isPrimary: false },
          })
        }

        const updateData: Record<string, unknown> = {}
        if (args.imageUrl) updateData.imageUrl = args.imageUrl
        if (args.isPrimary !== undefined) updateData.isPrimary = args.isPrimary

        return ctx.prisma.productImage.update({
          ...query,
          where: { id: args.id },
          data: updateData,
        })
      },
    }),

    deleteProductImage: t.field({
      type: 'Boolean',
      args: {
        id: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.productImage.delete({
            where: { id: args.id },
          })
          return true
        } catch {
          throw new Error('Failed to delete product image')
        }
      },
    }),

    // ProductTag mutations (for many-to-many relationship)
    addTagToProduct: t.prismaField({
      type: 'ProductTag',
      args: {
        productId: t.arg.string({ required: true }),
        tagId: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        // Check if association already exists
        const existing = await ctx.prisma.productTag.findFirst({
          where: {
            productId: args.productId,
            tagId: args.tagId,
          },
        })

        if (existing) {
          throw new Error('Tag already associated with this product')
        }

        return ctx.prisma.productTag.create({
          ...query,
          data: {
            productId: args.productId,
            tagId: args.tagId,
          },
        })
      },
    }),

    removeTagFromProduct: t.field({
      type: 'Boolean',
      args: {
        productId: t.arg.string({ required: true }),
        tagId: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.productTag.deleteMany({
            where: {
              productId: args.productId,
              tagId: args.tagId,
            },
          })
          return true
        } catch {
          throw new Error('Failed to remove tag from product')
        }
      },
    }),

    // VariantDiscount mutations
    applyDiscountToVariant: t.prismaField({
      type: 'VariantDiscount',
      args: {
        productVariantId: t.arg.string({ required: true }),
        discountId: t.arg.string({ required: true }),
      },
      resolve: async (query, root, args, ctx) => {
        // Check if association already exists
        const existing = await ctx.prisma.variantDiscount.findFirst({
          where: {
            productVariantId: args.productVariantId,
            discountId: args.discountId,
          },
        })

        if (existing) {
          throw new Error('Discount already applied to this variant')
        }

        return ctx.prisma.variantDiscount.create({
          ...query,
          data: {
            productVariantId: args.productVariantId,
            discountId: args.discountId,
          },
        })
      },
    }),

    removeDiscountFromVariant: t.field({
      type: 'Boolean',
      args: {
        productVariantId: t.arg.string({ required: true }),
        discountId: t.arg.string({ required: true }),
      },
      resolve: async (root, args, ctx) => {
        try {
          await ctx.prisma.variantDiscount.deleteMany({
            where: {
              productVariantId: args.productVariantId,
              discountId: args.discountId,
            },
          })
          return true
        } catch {
          throw new Error('Failed to remove discount from variant')
        }
      },
    }),
  }),
})
