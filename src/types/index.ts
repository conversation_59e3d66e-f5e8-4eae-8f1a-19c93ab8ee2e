export interface Product {
  id: string;
  title: string;
  description: string;
  image: string;
  price?: number;
  category?: string;
  tags?: string[];
}

export interface NavigationItem {
  label: string;
  href: string;
}

export interface SocialLinks {
  twitter?: string;
  instagram?: string;
  facebook?: string;
}

export interface HeroData {
  backgroundImage: string;
  title: string;
  subtitle: string;
  ctaText: string;
}

export interface NewsletterData {
  title: string;
  description: string;
  ctaText: string;
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  text: string;
}
