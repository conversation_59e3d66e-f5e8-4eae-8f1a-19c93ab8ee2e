'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Box, Container, Typography } from '@mui/material';
import {
  Layout,
  SearchBar,
  FilterChips,
  SortDropdown,
  ProductGrid,
  Breadcrumb,
} from '@/components';
import { searchResults, filterChips as initialFilterChips, sortOptions } from '@/data/searchData';
import { Product } from '@/types';

interface FilterChip {
  id: string;
  label: string;
  active: boolean;
}

export default function SearchPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('T-shirts');
  const [filterChips, setFilterChips] = useState<FilterChip[]>(initialFilterChips);
  const [sortValue, setSortValue] = useState('recommended');
  const [filteredResults, setFilteredResults] = useState<Product[]>(searchResults);

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
  };

  const handleSearchSubmit = () => {
    console.log('Searching for:', searchQuery);
    // Implement search logic here
  };

  const handleSearchClear = () => {
    setSearchQuery('');
    // Reset to all results or handle empty search
  };

  const handleChipClick = (chipId: string) => {
    const updatedChips = filterChips.map(chip => ({
      ...chip,
      active: chip.id === chipId
    }));
    setFilterChips(updatedChips);

    // Filter results based on selected chip
    if (chipId === 'all') {
      setFilteredResults(searchResults);
    } else {
      const filtered = searchResults.filter(product => 
        product.category === chipId
      );
      setFilteredResults(filtered);
    }
  };

  const handleSortChange = (value: string) => {
    setSortValue(value);
    
    // Sort results based on selected option
    const sortedResults = [...filteredResults];
    switch (value) {
      case 'price-low':
        sortedResults.sort((a, b) => (a.price || 0) - (b.price || 0));
        break;
      case 'price-high':
        sortedResults.sort((a, b) => (b.price || 0) - (a.price || 0));
        break;
      case 'newest':
        // Assume reverse order for newest
        sortedResults.reverse();
        break;
      default:
        // Keep original order for recommended
        break;
    }
    setFilteredResults(sortedResults);
  };

  const handleProductClick = (product: Product) => {
    console.log('Product clicked:', product);
    router.push(`/productdetail?id=${product.id}`);
  };

  const headerProps = {
    navigationItems: [
      { label: "New Arrivals", href: "/searchpage?category=new" },
      { label: "Featured", href: "/searchpage?category=featured" },
      { label: "Sale", href: "/searchpage?category=sale" },
      { label: "About Us", href: "/contactus" },
    ]
  };

  return (
    <Layout headerProps={headerProps}>
      <Container maxWidth="lg" sx={{ py: { xs: 2, md: 2.5 } }}>
        {/* Breadcrumb */}
        <Box sx={{ px: { xs: 2, md: 2 }, mb: { xs: 1, md: 2 } }}>
          <Breadcrumb
            items={[
              { label: 'Home', href: '/' },
              { label: 'Search', href: '/searchpage' },
            ]}
          />
        </Box>

        {/* Main Search Bar */}
        <Box sx={{ px: { xs: 2, md: 2 }, py: 1.5 }}>
          <SearchBar
            placeholder="Search for products..."
            value={searchQuery}
            onChange={handleSearchChange}
            onSearch={handleSearchSubmit}
            onClear={handleSearchClear}
            size="large"
          />
        </Box>

        {/* Filter Chips */}
        <FilterChips
          chips={filterChips}
          onChipClick={handleChipClick}
        />

        {/* Filters Section */}
        <Box sx={{ px: { xs: 2, md: 2 }, py: 2 }}>
          <Typography
            variant="h3"
            sx={{
              color: 'text.primary',
              fontSize: { xs: '1rem', md: '1.125rem' },
              fontWeight: 700,
              mb: 2,
            }}
          >
            Filters
          </Typography>
          
          <Box sx={{ maxWidth: 480 }}>
            <SortDropdown
              value={sortValue}
              options={sortOptions}
              onChange={handleSortChange}
            />
          </Box>
        </Box>

        {/* Results */}
        <ProductGrid
          title=""
          items={filteredResults}
          onItemClick={handleProductClick}
          columns={{ xs: 2, sm: 3, md: 4, lg: 4 }}
        />
      </Container>
    </Layout>
  );
}
