
'use client';

import React from 'react';
import { Container, Box, Typography, Stack } from '@mui/material';
import { 
  Layout, 
  Breadcrumb, 
  ShippingOption, 
  Button 
} from '@/components';
import { shippingOptions, shippingPageData } from '@/data/shippingData';

const ShippingPage = () => {
  const handleStartReturn = () => {
    console.log('Start return clicked');
    // Handle return process
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: { xs: 3, md: 5 } }}>
        {/* Breadcrumb */}
        <Box sx={{ mb: { xs: 2, md: 3 }, px: { xs: 2, md: 0 } }}>
          <Breadcrumb items={shippingPageData.breadcrumbs} />
        </Box>

        {/* Page Title */}
        <Box sx={{ mb: { xs: 3, md: 4 }, px: { xs: 2, md: 0 } }}>
          <Typography
            variant="h3"
            sx={{
              color: 'text.primary',
              fontSize: { xs: '1.5rem', md: '2rem' },
              fontWeight: 700,
              lineHeight: 1.2,
            }}
          >
            {shippingPageData.title}
          </Typography>
        </Box>

        <Box sx={{ maxWidth: 960, mx: 'auto', px: { xs: 2, md: 0 } }}>
          {/* Shipping Options Section */}
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h4"
              sx={{
                color: 'text.primary',
                fontSize: { xs: '1.25rem', md: '1.5rem' },
                fontWeight: 700,
                mb: 2,
              }}
            >
              {shippingPageData.sections.shipping.title}
            </Typography>
            
            <Typography
              variant="body1"
              sx={{
                color: 'text.primary',
                lineHeight: 1.6,
                mb: 3,
              }}
            >
              {shippingPageData.sections.shipping.description}
            </Typography>

            <Stack spacing={1}>
              {shippingOptions.map((option) => (
                <ShippingOption
                  key={option.id}
                  title={option.title}
                  description={option.description}
                  price={option.price}
                />
              ))}
            </Stack>
          </Box>

          {/* Delivery Times Section */}
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h4"
              sx={{
                color: 'text.primary',
                fontSize: { xs: '1.25rem', md: '1.5rem' },
                fontWeight: 700,
                mb: 2,
              }}
            >
              {shippingPageData.sections.delivery.title}
            </Typography>
            
            <Typography
              variant="body1"
              sx={{
                color: 'text.primary',
                lineHeight: 1.6,
              }}
            >
              {shippingPageData.sections.delivery.description}
            </Typography>
          </Box>

          {/* Returns & Exchanges Section */}
          <Box sx={{ mb: 4 }}>
            <Typography
              variant="h4"
              sx={{
                color: 'text.primary',
                fontSize: { xs: '1.25rem', md: '1.5rem' },
                fontWeight: 700,
                mb: 2,
              }}
            >
              {shippingPageData.sections.returns.title}
            </Typography>
            
            <Typography
              variant="body1"
              sx={{
                color: 'text.primary',
                lineHeight: 1.6,
                mb: 3,
              }}
            >
              {shippingPageData.sections.returns.description}
            </Typography>

            <Button
              variant="secondary"
              onClick={handleStartReturn}
              sx={{ alignSelf: 'flex-start' }}
            >
              Start a Return
            </Button>
          </Box>
        </Box>
      </Container>
    </Layout>
  );
};

export default ShippingPage;
