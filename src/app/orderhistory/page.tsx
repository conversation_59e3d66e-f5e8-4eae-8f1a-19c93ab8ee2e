'use client';

import React from 'react';
import { Container, Box, Typography, Stack } from '@mui/material';
import { Layout, OrderCard, Breadcrumb } from '@/components';
import { orderHistoryData } from '@/data/orderHistoryData';

const OrderHistory = () => {
  const handleOrderClick = (orderId: string) => {
    console.log('Order clicked:', orderId);
    // Navigate to order detail page
  };

  const headerProps = {
    navigationItems: [
      { label: "New Arrivals", href: "/searchpage?category=new" },
      { label: "Featured", href: "/searchpage?category=featured" },
      { label: "Sale", href: "/searchpage?category=sale" },
      { label: "About Us", href: "/contactus" },
    ]
  };

  return (
    <Layout headerProps={headerProps}>
      <Container maxWidth="lg" sx={{ py: { xs: 3, md: 5 } }}>
        {/* Breadcrumb */}
        <Box sx={{ mb: { xs: 2, md: 3 }, px: { xs: 2, md: 0 } }}>
          <Breadcrumb
            items={[
              { label: 'Home', href: '/' },
              { label: 'Order History', href: '/orderhistory' },
            ]}
          />
        </Box>

        {/* Page Title */}
        <Box sx={{ mb: { xs: 3, md: 4 }, px: { xs: 2, md: 0 } }}>
          <Typography
            variant="h3"
            sx={{
              color: 'text.primary',
              fontSize: { xs: '1.5rem', md: '2rem' },
              fontWeight: 700,
              lineHeight: 1.2,
            }}
          >
            Orders
          </Typography>
        </Box>

        {/* Orders List */}
        <Box sx={{ px: { xs: 2, md: 0 } }}>
          <Stack spacing={3}>
            {orderHistoryData.map((order) => (
              <OrderCard
                key={order.id}
                id={order.id}
                productName={order.productName}
                description={order.description}
                status={order.status}
                image={order.image}
                orderDate={order.orderDate}
                price={order.price}
                onClick={() => handleOrderClick(order.id)}
              />
            ))}
          </Stack>
        </Box>
      </Container>
    </Layout>
  );
};

export default OrderHistory;