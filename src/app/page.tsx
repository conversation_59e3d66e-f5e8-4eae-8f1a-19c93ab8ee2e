'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Box } from '@mui/material';
import {
  Layout,
  Hero,
  FeaturedItems,
  ProductGrid,
  NewsletterSection,
} from '@/components';
import { featuredItems, newArrivals, heroData, newsletterData } from '@/data/homeData';
import { Product } from '@/types';

export default function Home() {
  const router = useRouter();

  const handleProductClick = (product: Product) => {
    console.log('Product clicked:', product);
    router.push(`/productdetail?id=${product.id}`);
  };

  const handleHeroCtaClick = () => {
    console.log('Hero CTA clicked');
    router.push('/searchpage');
  };

  const handleNewsletterSignup = () => {
    console.log('Newsletter signup clicked');
    // Handle newsletter signup
  };

  return (
    <Layout>
      <Box sx={{ pb: 2 }}>
        {/* Hero Section */}
        <Hero
          backgroundImage={heroData.backgroundImage}
          title={heroData.title}
          subtitle={heroData.subtitle}
          ctaText={heroData.ctaText}
          onCtaClick={handleHeroCtaClick}
        />

        {/* Featured Items Section */}
        <FeaturedItems
          title="Featured Items"
          items={featuredItems}
          onItemClick={handleProductClick}
        />

        {/* New Arrivals Section */}
        <ProductGrid
          title="New Arrivals"
          items={newArrivals}
          onItemClick={handleProductClick}
          columns={{ xs: 2, sm: 2, md: 4, lg: 4 }}
        />

        {/* Newsletter Section */}
        <NewsletterSection
          title={newsletterData.title}
          description={newsletterData.description}
          ctaText={newsletterData.ctaText}
          onCtaClick={handleNewsletterSignup}
        />
      </Box>
    </Layout>
  );
}
