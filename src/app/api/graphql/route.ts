import { create<PERSON>oga } from 'graphql-yoga'
import { schema } from '../../../graphql/schema'
import { prisma } from '../../../lib/prisma'

const yoga = createYoga({
  schema,
  // Configure GraphQL endpoint
  graphqlEndpoint: '/api/graphql',
  
  // CORS configuration
  cors: {
    origin: process.env.NODE_ENV === 'production'
      ? ['https://yourdomain.com'] // Replace with your production domain
      : ['http://localhost:3000', 'http://127.0.0.1:3000'], // Allow specific origins in development
    credentials: true,
  },

  // Enable GraphQL playground in development
  graphiql: process.env.NODE_ENV === 'development',

  // Context function to provide Prisma client and other dependencies
  context: async () => ({
    prisma,
  }),

  // Logging configuration
  logging: process.env.NODE_ENV === 'development',

  // Mask errors in production
  maskedErrors: process.env.NODE_ENV === 'production',
})

// Handle all HTTP methods with the same handler
export async function GET(request: Request) {
  return yoga.fetch(request)
}

export async function POST(request: Request) {
  return yoga.fetch(request)
}

export async function OPTIONS(request: Request) {
  return yoga.fetch(request)
}