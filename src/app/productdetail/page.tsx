'use client';

import React, { useState } from 'react';
import { Container, Box } from '@mui/material';
import {
  Layout,
  Breadcrumb,
  ProductImageGrid,
  ProductDetails,
  SizeSelector,
  ProductActions,
  ReviewSummary,
} from '@/components';
import ColorSelector from '@/components/ui/ColorSelector';
import { productDetailData, reviewBreakdown, breadcrumbItems } from '@/data/productDetailData';

const ProductDetailPage = () => {
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [isFavorite, setIsFavorite] = useState(false);

  const handleSizeChange = (sizeId: string) => {
    setSelectedSize(sizeId);
    console.log('Selected size:', sizeId);
  };

  const handleColorChange = (colorId: string) => {
    setSelectedColor(colorId);
    console.log('Selected color:', colorId);
  };

  const handleFavoriteToggle = () => {
    setIsFavorite(!isFavorite);
    console.log('Favorite toggled:', !isFavorite);
  };

  const handleAddToCart = () => {
    console.log('Added to cart:', {
      product: productDetailData.id,
      size: selectedSize,
      color: selectedColor,
    });
  };

  const handleAddToWishlist = () => {
    setIsFavorite(!isFavorite);
    console.log('Added to wishlist:', productDetailData.id);
  };

  const handleShare = () => {
    console.log('Share product:', productDetailData.id);
  };

  const handleLike = () => {
    console.log('Like product');
  };

  const handleDislike = () => {
    console.log('Dislike product');
  };

  const headerProps = {
    navigationItems: [
      { label: "New Arrivals", href: "/searchpage?category=new" },
      { label: "Featured", href: "/searchpage?category=featured" },
      { label: "Sale", href: "/searchpage?category=sale" },
      { label: "About Us", href: "/contactus" },
    ]
  };

  return (
    <Layout headerProps={headerProps}>
      <Container maxWidth="lg" sx={{ py: { xs: 2, md: 2.5 } }}>
        {/* Breadcrumb */}
        <Box sx={{ px: { xs: 2, md: 0 }, mb: { xs: 2, md: 3 } }}>
          <Breadcrumb items={breadcrumbItems} />
        </Box>

        <Box sx={{ maxWidth: 960, mx: 'auto', px: { xs: 2, md: 0 } }}>
          {/* Product Images */}
          <ProductImageGrid images={productDetailData.images} />

          {/* Product Details */}
          <ProductDetails
            title={productDetailData.title}
            brand={productDetailData.brand}
            price={productDetailData.price}
            originalPrice={productDetailData.originalPrice}
            rating={productDetailData.rating}
            reviewCount={productDetailData.reviewCount}
            isFavorite={isFavorite}
            onFavoriteToggle={handleFavoriteToggle}
            description={productDetailData.description}
            features={productDetailData.features}
          />

          {/* Size Selector */}
          <SizeSelector
            sizes={productDetailData.sizes}
            selectedSize={selectedSize}
            onSizeChange={handleSizeChange}
          />

          {/* Color Selector */}
          <ColorSelector
            colors={productDetailData.colors}
            selectedColor={selectedColor}
            onColorChange={handleColorChange}
          />

          {/* Product Actions */}
          <ProductActions
            onAddToCart={handleAddToCart}
            onAddToWishlist={handleAddToWishlist}
            onShare={handleShare}
            onLike={handleLike}
            onDislike={handleDislike}
            likeCount={25}
            dislikeCount={3}
            isInWishlist={isFavorite}
            disabled={!selectedSize || !selectedColor}
          />

          {/* Reviews */}
          <ReviewSummary
            averageRating={productDetailData.rating}
            totalReviews={productDetailData.reviewCount}
            ratingBreakdown={reviewBreakdown}
          />
        </Box>
      </Container>
    </Layout>
  );
};

export default ProductDetailPage;
