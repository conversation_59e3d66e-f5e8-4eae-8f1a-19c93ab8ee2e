'use client';

import React from 'react';
import { Container, Box, Typography } from '@mui/material';
import { Layout, ContactForm, Breadcrumb } from '@/components';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const ContactUs = () => {
  const handleFormSubmit = (formData: ContactFormData) => {
    console.log('Contact form submitted:', formData);
    // Here you would typically send the data to your backend
  };

  const headerProps = {
    navigationItems: [
      { label: "New Arrivals", href: "/searchpage?category=new" },
      { label: "Featured", href: "/searchpage?category=featured" },
      { label: "Sale", href: "/searchpage?category=sale" },
      { label: "About Us", href: "/contactus" },
    ]
  };

  return (
    <Layout headerProps={headerProps}>
      <Container maxWidth="lg" sx={{ py: { xs: 3, md: 5 } }}>
        {/* Breadcrumb */}
        <Box sx={{ mb: { xs: 2, md: 3 }, px: { xs: 2, md: 0 } }}>
          <Breadcrumb
            items={[
              { label: 'Home', href: '/' },
              { label: 'Contact Us', href: '/contactus' },
            ]}
          />
        </Box>

        {/* Page Title */}
        <Box sx={{ mb: { xs: 3, md: 4 }, px: { xs: 2, md: 0 } }}>
          <Typography
            variant="h3"
            sx={{
              color: 'text.primary',
              fontSize: { xs: '1.5rem', md: '2rem' },
              fontWeight: 700,
              lineHeight: 1.2,
            }}
          >
            Contact Us
          </Typography>
        </Box>

        {/* Contact Form */}
        <Box sx={{ px: { xs: 2, md: 0 } }}>
          <ContactForm onSubmit={handleFormSubmit} />
        </Box>
      </Container>
    </Layout>
  );
};

export default ContactUs;
