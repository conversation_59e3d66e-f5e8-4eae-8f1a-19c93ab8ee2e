'use client';

import React from 'react';
import { Box, Typography, Rating, LinearProgress } from '@mui/material';
import { Star } from '@mui/icons-material';

interface ReviewSummaryProps {
  averageRating: number;
  totalReviews: number;
  ratingBreakdown: {
    stars: number;
    percentage: number;
  }[];
}

const ReviewSummary: React.FC<ReviewSummaryProps> = ({
  averageRating,
  totalReviews,
  ratingBreakdown,
}) => {
  return (
    <Box sx={{ px: 2, py: 3 }}>
      <Typography
        variant="h6"
        sx={{
          color: 'text.primary',
          fontSize: '1.125rem',
          fontWeight: 700,
          mb: 2,
        }}
      >
        Customer Reviews
      </Typography>

      {/* Overall Rating */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, gap: 1 }}>
        <Rating
          value={averageRating}
          precision={0.1}
          readOnly
          icon={<Star fontSize="inherit" />}
          emptyIcon={<Star fontSize="inherit" />}
          sx={{
            color: 'primary.main',
            '& .MuiRating-iconEmpty': {
              color: 'divider',
            },
          }}
        />
        <Typography
          sx={{
            color: 'text.primary',
            fontSize: '1rem',
            fontWeight: 'normal',
          }}
        >
          {totalReviews} reviews
        </Typography>
      </Box>

      {/* Rating Breakdown */}
      <Box sx={{ maxWidth: 400 }}>
        {ratingBreakdown.map((item) => (
          <Box
            key={item.stars}
            sx={{
              display: 'grid',
              gridTemplateColumns: '20px 1fr 60px',
              alignItems: 'center',
              gap: 1.5,
              mb: 1.5,
            }}
          >
            <Typography
              sx={{
                color: 'text.primary',
                fontSize: '0.875rem',
                fontWeight: 'normal',
              }}
            >
              {item.stars}
            </Typography>
            
            <LinearProgress
              variant="determinate"
              value={item.percentage}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'divider',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'primary.main',
                  borderRadius: 4,
                },
              }}
            />
            
            <Typography
              sx={{
                color: 'text.secondary',
                fontSize: '0.875rem',
                fontWeight: 'normal',
                textAlign: 'right',
              }}
            >
              {item.percentage}%
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default ReviewSummary;
