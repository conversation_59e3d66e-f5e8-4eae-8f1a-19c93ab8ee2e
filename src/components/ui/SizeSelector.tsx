'use client';

import React, { useState } from 'react';
import { Box, Chip, Typography } from '@mui/material';

interface Size {
  id: string;
  label: string;
  available?: boolean;
}

interface SizeSelectorProps {
  sizes: Size[];
  selectedSize?: string;
  onSizeChange?: (sizeId: string) => void;
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  sizes,
  selectedSize,
  onSizeChange,
}) => {
  const [selected, setSelected] = useState(selectedSize || '');

  const handleSizeSelect = (sizeId: string) => {
    setSelected(sizeId);
    onSizeChange?.(sizeId);
  };

  return (
    <Box>
      <Typography
        variant="h6"
        sx={{
          color: 'text.primary',
          fontSize: '1.125rem',
          fontWeight: 700,
          mb: 2,
          px: 2,
        }}
      >
        Size
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1.5, px: 2 }}>
        {sizes.map((size) => (
          <Chip
            key={size.id}
            label={size.label}
            clickable={size.available !== false}
            variant={selected === size.id ? 'filled' : 'outlined'}
            color={selected === size.id ? 'primary' : 'default'}
            disabled={size.available === false}
            onClick={() => size.available !== false && handleSizeSelect(size.id)}
            sx={{
              minWidth: 48,
              height: 44,
              fontWeight: 500,
              fontSize: '0.875rem',
              borderColor: 'divider',
              '&.MuiChip-filled': {
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                borderWidth: 2,
                borderStyle: 'solid',
                borderColor: 'primary.main',
              },
              '&.MuiChip-outlined': {
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              },
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

export default SizeSelector;
