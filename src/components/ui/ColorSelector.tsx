'use client';

import React, { useState } from 'react';
import { Box, Typography, Radio, FormControlLabel, RadioGroup } from '@mui/material';

interface Color {
  id: string;
  name: string;
  value: string;
}

interface ColorSelectorProps {
  colors: Color[];
  selectedColor?: string;
  onColorChange?: (colorId: string) => void;
}

const ColorSelector: React.FC<ColorSelectorProps> = ({
  colors,
  selectedColor,
  onColorChange,
}) => {
  const [selected, setSelected] = useState(selectedColor || '');

  const handleColorSelect = (colorId: string) => {
    setSelected(colorId);
    onColorChange?.(colorId);
  };

  return (
    <Box>
      <Typography
        variant="h6"
        sx={{
          color: 'text.primary',
          fontSize: '1.125rem',
          fontWeight: 700,
          mb: 2,
          px: 2,
        }}
      >
        Color
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2.5, px: 2 }}>
        <RadioGroup
          value={selected}
          onChange={(e) => handleColorSelect(e.target.value)}
          sx={{ display: 'flex', flexDirection: 'row', gap: 2.5 }}
        >
          {colors.map((color) => (
            <FormControlLabel
              key={color.id}
              value={color.id}
              control={
                <Radio
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    backgroundColor: color.value,
                    border: '1px solid',
                    borderColor: 'divider',
                    margin: 0,
                    '&.Mui-checked': {
                      borderWidth: 3,
                      borderColor: 'background.paper',
                      boxShadow: `0 0 0 2px ${color.value}`,
                    },
                    '& .MuiSvgIcon-root': {
                      display: 'none',
                    },
                  }}
                />
              }
              label=""
              sx={{
                margin: 0,
                '& .MuiFormControlLabel-label': {
                  display: 'none',
                },
              }}
            />
          ))}
        </RadioGroup>
      </Box>
    </Box>
  );
};

export default ColorSelector;
