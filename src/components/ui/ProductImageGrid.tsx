'use client';

import React from 'react';
import { Box, Grid } from '@mui/material';

interface ProductImageGridProps {
  images: string[];
}

const ProductImageGrid: React.FC<ProductImageGridProps> = ({ images }) => {
  return (
    <Box sx={{ width: '100%', p: 2 }}>
      <Grid 
        container 
        spacing={0.5}
        sx={{
          aspectRatio: '2/3',
          overflow: 'hidden',
          borderRadius: 1.5,
        }}
      >
        {/* Main large image */}
        <Grid size={12} sx={{ height: '50%' }}>
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundImage: `url("${images[0]}")`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          />
        </Grid>
        
        {/* Two smaller images */}
        <Grid size={6} sx={{ height: '25%' }}>
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundImage: `url("${images[1] || images[0]}")`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          />
        </Grid>
        
        <Grid size={6} sx={{ height: '25%' }}>
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundImage: `url("${images[2] || images[0]}")`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          />
        </Grid>
        
        <Grid size={6} sx={{ height: '25%' }}>
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundImage: `url("${images[3] || images[0]}")`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          />
        </Grid>
        
        <Grid size={6} sx={{ height: '25%' }}>
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundImage: `url("${images[4] || images[0]}")`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProductImageGrid;
