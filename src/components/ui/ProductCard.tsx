'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardMedia, Typography } from '@mui/material';

interface ProductCardProps {
  id?: string | number;
  image: string;
  title: string;
  description: string;
  onClick?: () => void;
  aspectRatio?: string;
  variant?: 'featured' | 'grid';
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  image,
  title,
  description,
  onClick,
  aspectRatio = '3/4',
  variant = 'grid'
}) => {
  const isHorizontal = variant === 'featured';
  
  const CardComponent = (
    <Card
      sx={{
        cursor: 'pointer',
        border: 'none',
        boxShadow: 'none',
        backgroundColor: 'transparent',
        minWidth: isHorizontal ? 240 : 158,
        flexShrink: 0,
        '&:hover': {
          transform: 'translateY(-2px)',
          transition: 'transform 0.2s ease-in-out',
        },
      }}
      onClick={onClick}
    >
      <CardMedia
        component="div"
        sx={{
          aspectRatio: aspectRatio,
          backgroundImage: `url("${image}")`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          borderRadius: 3,
          mb: 2,
        }}
      />
      
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Typography
          variant="body1"
          sx={{
            color: 'text.primary',
            fontWeight: 500,
            lineHeight: 1.5,
            mb: 0.5,
          }}
        >
          {title}
        </Typography>
        
        <Typography
          variant="body2"
          sx={{
            color: 'text.secondary',
            lineHeight: 1.5,
          }}
        >
          {description}
        </Typography>
      </CardContent>
    </Card>
  );

  // If there's an ID and no custom onClick handler, wrap with Link
  if (id && !onClick) {
    return (
      <Link href={`/productdetail?id=${id}`} style={{ textDecoration: 'none' }}>
        {CardComponent}
      </Link>
    );
  }

  return CardComponent;
};

export default ProductCard;
