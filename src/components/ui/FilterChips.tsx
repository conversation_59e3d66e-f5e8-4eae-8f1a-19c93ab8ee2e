'use client';

import React from 'react';
import { Box, Chip } from '@mui/material';

interface FilterChip {
  id: string;
  label: string;
  active?: boolean;
}

interface FilterChipsProps {
  chips: FilterChip[];
  onChipClick?: (chipId: string) => void;
}

const FilterChips: React.FC<FilterChipsProps> = ({
  chips,
  onChipClick
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        gap: 1.5,
        flexWrap: 'wrap',
        p: 1.5,
      }}
    >
      {chips.map((chip) => (
        <Chip
          key={chip.id}
          label={chip.label}
          clickable
          onClick={() => onChipClick?.(chip.id)}
          sx={{
            bgcolor: chip.active ? 'primary.main' : 'primary.light',
            color: chip.active ? 'primary.contrastText' : 'text.primary',
            fontWeight: 500,
            fontSize: '0.875rem',
            height: 32,
            '&:hover': {
              bgcolor: chip.active ? 'primary.dark' : 'primary.light',
              opacity: 0.8,
            },
          }}
        />
      ))}
    </Box>
  );
};

export default FilterChips;
