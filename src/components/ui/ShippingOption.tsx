'use client';

import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

interface ShippingOptionProps {
  title: string;
  description: string;
  price: string;
}

const ShippingOption: React.FC<ShippingOptionProps> = ({
  title,
  description,
  price,
}) => {
  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        mb: 1,
        bgcolor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 2,
        '&:hover': {
          bgcolor: 'action.hover',
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              color: 'text.primary',
              mb: 0.5,
            }}
          >
            {title}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
            }}
          >
            {description}
          </Typography>
        </Box>
        <Typography
          variant="body1"
          sx={{
            fontWeight: 500,
            color: 'text.primary',
          }}
        >
          {price}
        </Typography>
      </Box>
    </Paper>
  );
};

export default ShippingOption;
