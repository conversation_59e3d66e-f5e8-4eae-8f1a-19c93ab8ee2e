'use client';

import React from 'react';
import { Box, IconButton } from '@mui/material';
import { ShoppingCart, Favorite, Share, ThumbUp, ThumbDown } from '@mui/icons-material';
import Button from './Button';

interface ProductActionsProps {
  onAddToCart?: () => void;
  onAddToWishlist?: () => void;
  onShare?: () => void;
  onLike?: () => void;
  onDislike?: () => void;
  likeCount?: number;
  dislikeCount?: number;
  isInWishlist?: boolean;
  disabled?: boolean;
}

const ProductActions: React.FC<ProductActionsProps> = ({
  onAddToCart,
  onAddToWishlist,
  onShare,
  onLike,
  onDislike,
  likeCount = 0,
  dislikeCount = 0,
  isInWishlist = false,
  disabled = false,
}) => {
  return (
    <Box sx={{ px: 2, py: 3 }}>
      {/* Main Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button
          variant="primary"
          fullWidth
          onClick={onAddToCart}
          disabled={disabled}
          startIcon={<ShoppingCart />}
          sx={{
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 600,
          }}
        >
          Add to Cart
        </Button>
        
        <Button
          variant="outline"
          onClick={onAddToWishlist}
          disabled={disabled}
          startIcon={<Favorite />}
          sx={{
            py: 1.5,
            px: 3,
            fontSize: '1rem',
            fontWeight: 600,
            minWidth: 'auto',
            color: isInWishlist ? 'primary.main' : 'text.primary',
            borderColor: isInWishlist ? 'primary.main' : 'divider',
          }}
        >
          {isInWishlist ? 'Saved' : 'Save'}
        </Button>
      </Box>

      {/* Secondary Actions */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <IconButton
          onClick={onShare}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              color: 'primary.main',
            },
          }}
        >
          <Share />
        </IconButton>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <IconButton
              onClick={onLike}
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'success.main',
                },
              }}
            >
              <ThumbUp fontSize="small" />
            </IconButton>
            <Box
              component="span"
              sx={{
                color: 'text.secondary',
                fontSize: '0.875rem',
                minWidth: 20,
                textAlign: 'center',
              }}
            >
              {likeCount}
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <IconButton
              onClick={onDislike}
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'error.main',
                },
              }}
            >
              <ThumbDown fontSize="small" />
            </IconButton>
            <Box
              component="span"
              sx={{
                color: 'text.secondary',
                fontSize: '0.875rem',
                minWidth: 20,
                textAlign: 'center',
              }}
            >
              {dislikeCount}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ProductActions;
