'use client';

import React from 'react';
import { Box, Typography, Link } from '@mui/material';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, p: 2 }}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {item.href ? (
            <Link
              href={item.href}
              sx={{
                color: 'secondary.main',
                textDecoration: 'none',
                fontSize: '1rem',
                fontWeight: 500,
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {item.label}
            </Link>
          ) : (
            <Typography
              sx={{
                color: 'text.primary',
                fontSize: '1rem',
                fontWeight: 500,
              }}
            >
              {item.label}
            </Typography>
          )}
          {index < items.length - 1 && (
            <Typography
              sx={{
                color: 'secondary.main',
                fontSize: '1rem',
                fontWeight: 500,
              }}
            >
              /
            </Typography>
          )}
        </React.Fragment>
      ))}
    </Box>
  );
};

export default Breadcrumb;
