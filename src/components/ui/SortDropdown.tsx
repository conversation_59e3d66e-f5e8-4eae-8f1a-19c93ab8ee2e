'use client';

import React from 'react';
import { FormControl, InputLabel, Select, MenuItem, SelectChangeEvent } from '@mui/material';

interface SortOption {
  value: string;
  label: string;
}

interface SortDropdownProps {
  label?: string;
  value: string;
  options: SortOption[];
  onChange?: (value: string) => void;
}

const SortDropdown: React.FC<SortDropdownProps> = ({
  label = "Sort by",
  value,
  options,
  onChange
}) => {
  const handleChange = (event: SelectChangeEvent) => {
    onChange?.(event.target.value);
  };

  return (
    <FormControl 
      fullWidth 
      sx={{ 
        minWidth: 160,
        maxWidth: 480,
      }}
    >
      <InputLabel 
        sx={{ 
          color: 'text.primary',
          fontWeight: 500,
          fontSize: '1rem',
          mb: 1,
        }}
      >
        {label}
      </InputLabel>
      <Select
        value={value}
        label={label}
        onChange={handleChange}
        sx={{
          height: 56,
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'secondary.light',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'secondary.light',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: 'primary.main',
          },
        }}
      >
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default SortDropdown;
