'use client';

import React from 'react';
import Link from 'next/link';
import { Box, Card, CardContent, Typography, Chip } from '@mui/material';

interface OrderCardProps {
  id: string;
  productName: string;
  description: string;
  status: 'Return created' | 'Shipped' | 'Delivered';
  image: string;
  orderDate?: string;
  price?: number;
  onClick?: () => void;
}

const OrderCard: React.FC<OrderCardProps> = ({
  productName,
  description,
  status,
  image,
  orderDate,
  price,
  onClick,
}) => {
  const getStatusColor = (status: string): 'success' | 'info' | 'warning' | 'default' => {
    switch (status) {
      case 'Delivered':
        return 'success';
      case 'Shipped':
        return 'info';
      case 'Return created':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Card
      sx={{
        borderRadius: 3,
        boxShadow: 'none',
        border: '1px solid',
        borderColor: 'divider',
        cursor: onClick ? 'pointer' : 'default',
        '&:hover': onClick ? {
          boxShadow: 1,
        } : {},
      }}
      onClick={onClick}
    >
      <CardContent sx={{ p: 2 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: 2,
          }}
        >
          {/* Product Image */}
          <Box
            sx={{
              width: { xs: '100%', md: 200 },
              height: 150,
              backgroundImage: `url("${image}")`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              borderRadius: 2,
              flexShrink: 0,
            }}
          />

          {/* Order Details */}
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            {/* Status */}
            <Chip
              label={status}
              color={getStatusColor(status)}
              size="small"
              sx={{
                alignSelf: 'flex-start',
                fontSize: '0.75rem',
              }}
            />

            {/* Product Name */}
            <Typography
              variant="h6"
              sx={{
                color: 'text.primary',
                fontSize: '1.125rem',
                fontWeight: 700,
                lineHeight: 1.2,
              }}
            >
              {productName}
            </Typography>

            {/* Description */}
            <Typography
              sx={{
                color: 'text.secondary',
                fontSize: '1rem',
                fontWeight: 'normal',
              }}
            >
              {description}
            </Typography>

            {/* Additional Info */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'end', mt: 1 }}>
              <Box>
                {orderDate && (
                  <Typography
                    sx={{
                      color: 'text.secondary',
                      fontSize: '0.875rem',
                      fontWeight: 'normal',
                    }}
                  >
                    Order Date: {orderDate}
                  </Typography>
                )}
              </Box>
              
              {price && (
                <Typography
                  sx={{
                    color: 'text.primary',
                    fontSize: '1.125rem',
                    fontWeight: 600,
                  }}
                >
                  ${price}
                </Typography>
              )}
            </Box>

            {/* Shipping & Returns Link */}
            {(status === 'Delivered' || status === 'Return created') && (
              <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
                <Link href="/shipping" style={{ textDecoration: 'none' }}>
                  <Typography
                    sx={{
                      color: 'primary.main',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      '&:hover': {
                        textDecoration: 'underline',
                      },
                    }}
                  >
                    {status === 'Return created' ? 'Return Policy' : 'Start a Return'}
                  </Typography>
                </Link>
              </Box>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default OrderCard;
