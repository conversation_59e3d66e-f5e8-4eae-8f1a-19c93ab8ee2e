'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { 
  Box, 
  TextField, 
  Typography, 
  Card, 
  CardContent,
  Stack,
  Alert
} from '@mui/material';
import { Email, Phone } from '@mui/icons-material';
import Button from './Button';

interface ContactFormProps {
  onSubmit?: (formData: ContactFormData) => void;
}

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const ContactForm: React.FC<ContactFormProps> = ({ onSubmit }) => {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (field: keyof ContactFormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    onSubmit?.(formData);
    setSubmitted(true);
    
    // Reset form after submission
    setTimeout(() => {
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
      });
      setSubmitted(false);
    }, 3000);
  };

  return (
    <Box>
      {/* Contact Form */}
      <Box 
        component="form" 
        onSubmit={handleSubmit}
        sx={{ maxWidth: 480, mb: 4 }}
      >
        <Stack spacing={3}>
          <TextField
            label="Your Name"
            placeholder="Enter your name"
            value={formData.name}
            onChange={handleChange('name')}
            required
            fullWidth
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'background.paper',
              },
            }}
          />

          <TextField
            label="Email"
            placeholder="Enter your email"
            type="email"
            value={formData.email}
            onChange={handleChange('email')}
            required
            fullWidth
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'background.paper',
              },
            }}
          />

          <TextField
            label="Subject"
            placeholder="Enter the subject"
            value={formData.subject}
            onChange={handleChange('subject')}
            required
            fullWidth
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'background.paper',
              },
            }}
          />

          <TextField
            label="Message"
            placeholder="Enter your message"
            value={formData.message}
            onChange={handleChange('message')}
            required
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'background.paper',
              },
            }}
          />

          <Box>
            <Button
              type="submit"
              variant="primary"
              disabled={submitted}
              sx={{
                px: 4,
                py: 1.5,
                borderRadius: 50,
              }}
            >
              {submitted ? 'Submitted!' : 'Submit'}
            </Button>
          </Box>

          {submitted && (
            <Alert severity="success">
              Thank you for your message! We&apos;ll get back to you soon.
            </Alert>
          )}
        </Stack>
      </Box>

      {/* Other Contact Methods */}
      <Typography
        variant="h5"
        sx={{
          color: 'text.primary',
          fontSize: '1.125rem',
          fontWeight: 700,
          mb: 3,
        }}
      >
        Other ways to reach us
      </Typography>

      <Stack spacing={2}>
        {/* Email */}
        <Card
          sx={{
            boxShadow: 'none',
            backgroundColor: 'background.paper',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <CardContent sx={{ py: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: 1,
                  backgroundColor: 'action.hover',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Email sx={{ color: 'text.primary' }} />
              </Box>
              <Box>
                <Typography
                  sx={{
                    color: 'text.primary',
                    fontSize: '1rem',
                    fontWeight: 500,
                  }}
                >
                  Email
                </Typography>
                <Typography
                  sx={{
                    color: 'text.secondary',
                    fontSize: '0.875rem',
                  }}
                >
                  <EMAIL>
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Phone */}
        <Card
          sx={{
            boxShadow: 'none',
            backgroundColor: 'background.paper',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <CardContent sx={{ py: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: 1,
                  backgroundColor: 'action.hover',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Phone sx={{ color: 'text.primary' }} />
              </Box>
              <Box>
                <Typography
                  sx={{
                    color: 'text.primary',
                    fontSize: '1rem',
                    fontWeight: 500,
                  }}
                >
                  Phone
                </Typography>
                <Typography
                  sx={{
                    color: 'text.secondary',
                    fontSize: '0.875rem',
                  }}
                >
                  +****************
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Helpful Links */}
        <Card
          sx={{
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 3,
            boxShadow: 'none',
          }}
        >
          <CardContent sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                mb: 2,
                color: 'text.primary',
              }}
            >
              Helpful Links
            </Typography>
            <Stack spacing={1}>
              <Link href="/shipping" style={{ textDecoration: 'none' }}>
                <Typography
                  sx={{
                    color: 'primary.main',
                    fontSize: '0.875rem',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Shipping & Returns Information
                </Typography>
              </Link>
              <Link href="/orderhistory" style={{ textDecoration: 'none' }}>
                <Typography
                  sx={{
                    color: 'primary.main',
                    fontSize: '0.875rem',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Order History
                </Typography>
              </Link>
              <Link href="/searchpage" style={{ textDecoration: 'none' }}>
                <Typography
                  sx={{
                    color: 'primary.main',
                    fontSize: '0.875rem',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Browse Products
                </Typography>
              </Link>
            </Stack>
          </CardContent>
        </Card>
      </Stack>
    </Box>
  );
};

export default ContactForm;
