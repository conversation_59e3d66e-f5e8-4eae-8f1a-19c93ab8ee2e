'use client';

import React from 'react';
import { Typography, Box } from '@mui/material';

interface SectionTitleProps {
  title: string;
  align?: 'left' | 'center' | 'right';
  sx?: object;
}

const SectionTitle: React.FC<SectionTitleProps> = ({
  title,
  align = 'left',
  sx = {}
}) => {
  return (
    <Box sx={{ px: 2, py: 2, ...sx }}>
      <Typography
        variant="h3"
        sx={{
          color: 'text.primary',
          fontSize: '1.375rem',
          fontWeight: 700,
          lineHeight: 1.3,
          letterSpacing: '-0.015em',
          textAlign: align,
        }}
      >
        {title}
      </Typography>
    </Box>
  );
};

export default SectionTitle;
