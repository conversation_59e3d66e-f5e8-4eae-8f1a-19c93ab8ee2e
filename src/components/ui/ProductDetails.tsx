'use client';

import React from 'react';
import { Box, Typography, IconButton, Chip } from '@mui/material';
import { Favorite, FavoriteBorder } from '@mui/icons-material';

interface ProductDetailsProps {
  title: string;
  brand: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviewCount: number;
  isFavorite?: boolean;
  onFavoriteToggle?: () => void;
  description?: string;
  features?: string[];
}

const ProductDetails: React.FC<ProductDetailsProps> = ({
  title,
  brand,
  price,
  originalPrice,
  rating,
  reviewCount,
  isFavorite = false,
  onFavoriteToggle,
  description,
  features = [],
}) => {
  return (
    <Box sx={{ px: 2 }}>
      {/* Title and Favorite */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
        <Typography
          variant="h4"
          sx={{
            color: 'text.primary',
            fontSize: '1.375rem',
            fontWeight: 700,
            lineHeight: 1.2,
            flex: 1,
          }}
        >
          {title}
        </Typography>
        
        <IconButton
          onClick={onFavoriteToggle}
          sx={{
            color: isFavorite ? 'primary.main' : 'text.secondary',
            ml: 2,
          }}
        >
          {isFavorite ? <Favorite /> : <FavoriteBorder />}
        </IconButton>
      </Box>

      {/* Brand */}
      <Typography
        sx={{
          color: 'text.secondary',
          fontSize: '0.875rem',
          fontWeight: 'normal',
          mb: 1,
        }}
      >
        Brand: {brand}
      </Typography>

      {/* Rating and Reviews */}
      <Typography
        sx={{
          color: 'text.secondary',
          fontSize: '0.875rem',
          fontWeight: 'normal',
          mb: 2,
        }}
      >
        {rating} • {reviewCount} reviews
      </Typography>

      {/* Price */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <Typography
          variant="h5"
          sx={{
            color: 'text.primary',
            fontSize: '1.5rem',
            fontWeight: 700,
          }}
        >
          ${price}
        </Typography>
        
        {originalPrice && originalPrice > price && (
          <Typography
            sx={{
              color: 'text.secondary',
              fontSize: '1.125rem',
              fontWeight: 'normal',
              textDecoration: 'line-through',
            }}
          >
            ${originalPrice}
          </Typography>
        )}
      </Box>

      {/* Description */}
      {description && (
        <Typography
          sx={{
            color: 'text.primary',
            fontSize: '0.875rem',
            fontWeight: 'normal',
            lineHeight: 1.6,
            mb: 3,
          }}
        >
          {description}
        </Typography>
      )}

      {/* Features */}
      {features.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="h6"
            sx={{
              color: 'text.primary',
              fontSize: '1rem',
              fontWeight: 600,
              mb: 1,
            }}
          >
            Features
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {features.map((feature, index) => (
              <Chip
                key={index}
                label={feature}
                variant="outlined"
                size="small"
                sx={{
                  borderColor: 'divider',
                  color: 'text.secondary',
                }}
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default ProductDetails;
