'use client';

import React from 'react';
import { TextField, InputAdornment, IconButton } from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';

interface SearchFieldProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: () => void;
  size?: 'small' | 'medium';
  fullWidth?: boolean;
}

const SearchField: React.FC<SearchFieldProps> = ({
  placeholder = "Search...",
  value = "",
  onChange,
  onSearch,
  size = "medium",
  fullWidth = false
}) => {
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(event.target.value);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      onSearch?.();
    }
  };

  return (
    <TextField
      placeholder={placeholder}
      value={value}
      onChange={handleInputChange}
      onKeyPress={handleKeyPress}
      size={size}
      fullWidth={fullWidth}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <IconButton
              onClick={onSearch}
              edge="end"
              sx={{
                color: 'text.primary',
                '&:hover': {
                  color: 'primary.main',
                },
              }}
            >
              <SearchIcon />
            </IconButton>
          </InputAdornment>
        ),
      }}
      sx={{
        '& .MuiOutlinedInput-root': {
          minWidth: { xs: 200, md: 300 },
        },
      }}
    />
  );
};

export default SearchField;
