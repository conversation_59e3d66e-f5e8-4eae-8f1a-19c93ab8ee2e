'use client';

import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';

interface HeroProps {
  backgroundImage: string;
  title: string;
  subtitle: string;
  ctaText: string;
  onCtaClick?: () => void;
}

const Hero: React.FC<HeroProps> = ({
  backgroundImage,
  title,
  subtitle,
  ctaText,
  onCtaClick
}) => {
  return (
    <Container maxWidth="lg" sx={{ px: { xs: 2, md: 4 } }}>
      <Box sx={{ p: { xs: 1, md: 2 } }}>
        <Box
          sx={{
            position: 'relative',
            minHeight: { xs: 300, md: 480 },
            borderRadius: 3,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-end',
            alignItems: 'flex-start',
            px: { xs: 2, md: 5 },
            pb: { xs: 4, md: 5 },
            pt: { xs: 2, md: 4 },
            background: `linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("${backgroundImage}")`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        >
          <Box sx={{ maxWidth: { xs: '100%', md: '60%' } }}>
            <Typography
              variant="h1"
              sx={{
                color: 'white',
                fontSize: { xs: '2rem', md: '3rem' },
                fontWeight: 900,
                lineHeight: 1.2,
                letterSpacing: '-0.033em',
                mb: 1,
              }}
            >
              {title}
            </Typography>
            
            <Typography
              variant="body1"
              sx={{
                color: 'white',
                fontSize: { xs: '0.875rem', md: '1rem' },
                lineHeight: 1.5,
                mb: 3,
              }}
            >
              {subtitle}
            </Typography>
            
            <Button
              variant="contained"
              size="large"
              onClick={onCtaClick}
              sx={{
                px: { xs: 2, md: 3 },
                py: { xs: 1.25, md: 1.5 },
                fontSize: { xs: '0.875rem', md: '1rem' },
                fontWeight: 700,
              }}
            >
              {ctaText}
            </Button>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default Hero;
