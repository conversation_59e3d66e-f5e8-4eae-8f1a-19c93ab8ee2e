'use client';

import React from 'react';
import { Box, Container, Grid } from '@mui/material';
import ProductCard from '../ui/ProductCard';
import SectionTitle from '../ui/SectionTitle';

interface ProductGridItem {
  id: string;
  image: string;
  title: string;
  description: string;
}

interface ProductGridProps {
  title?: string;
  items: ProductGridItem[];
  onItemClick?: (item: ProductGridItem) => void;
  columns?: { xs: number; sm: number; md: number; lg: number };
}

const ProductGrid: React.FC<ProductGridProps> = ({
  title = "New Arrivals",
  items,
  onItemClick,
  columns = { xs: 2, sm: 3, md: 4, lg: 4 }
}) => {
  return (
    <Container maxWidth="lg" sx={{ px: 0 }}>
      <SectionTitle title={title} />
      
      <Box sx={{ px: { xs: 1, md: 2 } }}>
        <Grid 
          container 
          spacing={{ xs: 1, md: 1.5 }}
          sx={{
            justifyContent: 'flex-start',
          }}
        >
          {items.map((item) => (
            <Grid 
              key={item.id}
              size={{ 
                xs: 12/columns.xs, 
                sm: 12/columns.sm, 
                md: 12/columns.md, 
                lg: 12/columns.lg 
              }}
            >
              <ProductCard
                id={item.id}
                image={item.image}
                title={item.title}
                description={item.description}
                onClick={() => onItemClick?.(item)}
                variant="grid"
              />
            </Grid>
          ))}
        </Grid>
      </Box>
    </Container>
  );
};

export default ProductGrid;
