'use client';

import React from 'react';
import { Box, Container } from '@mui/material';
import ProductCard from '../ui/ProductCard';
import SectionTitle from '../ui/SectionTitle';

interface FeaturedItem {
  id: string;
  image: string;
  title: string;
  description: string;
}

interface FeaturedItemsProps {
  title?: string;
  items: FeaturedItem[];
  onItemClick?: (item: FeaturedItem) => void;
}

const FeaturedItems: React.FC<FeaturedItemsProps> = ({
  title = "Featured Items",
  items,
  onItemClick
}) => {
  return (
    <Container maxWidth="lg" sx={{ px: 0 }}>
      <SectionTitle title={title} />
      
      <Box
        sx={{
          display: 'flex',
          overflowX: 'auto',
          gap: { xs: 1, md: 1.5 },
          px: { xs: 1, md: 2 },
          py: 1,
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        }}
      >
        {items.map((item) => (
          <ProductCard
            key={item.id}
            id={item.id}
            image={item.image}
            title={item.title}
            description={item.description}
            onClick={() => onItemClick?.(item)}
            variant="featured"
          />
        ))}
      </Box>
    </Container>
  );
};

export default FeaturedItems;
