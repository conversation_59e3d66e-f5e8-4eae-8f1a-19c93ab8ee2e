'use client';

import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';

interface NewsletterSectionProps {
  title: string;
  description: string;
  ctaText: string;
  onCtaClick?: () => void;
}

const NewsletterSection: React.FC<NewsletterSectionProps> = ({
  title,
  description,
  ctaText,
  onCtaClick
}) => {
  return (
    <Container maxWidth="lg">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 3,
          px: 2,
          py: { xs: 5, md: 10 },
          textAlign: 'center',
        }}
      >
        <Box sx={{ maxWidth: 720 }}>
          <Typography
            variant="h1"
            sx={{
              color: 'text.primary',
              fontSize: { xs: '2rem', md: '2.5rem' },
              fontWeight: 700,
              lineHeight: 1.2,
              letterSpacing: '-0.033em',
              mb: 1,
            }}
          >
            {title}
          </Typography>
          
          <Typography
            variant="body1"
            sx={{
              color: 'text.primary',
              lineHeight: 1.5,
            }}
          >
            {description}
          </Typography>
        </Box>
        
        <Button
        //   variant="contained"
          size="large"
          onClick={onCtaClick}
          sx={{
            px: { xs: 2, md: 3 },
            py: { xs: 1.25, md: 1.5 },
            fontSize: { xs: '0.875rem', md: '1rem' },
            fontWeight: 700,
            width: { xs: '100%', sm: 'auto' },
            maxWidth: 480,
          }}
        >
          {ctaText}
        </Button>
      </Box>
    </Container>
  );
};

export default NewsletterSection;
