'use client';

import React from 'react';
import Link from 'next/link';
import { Box, Typography, Button, Container, IconButton } from '@mui/material';
import {
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  Facebook as FacebookIcon,
} from '@mui/icons-material';
import { footerNavigationItems } from '../../data/navigationData';

interface FooterLink {
  label: string;
  href: string;
}

interface FooterProps {
  links?: FooterLink[];
  socialLinks?: {
    twitter?: string;
    instagram?: string;
    facebook?: string;
  };
  copyrightText?: string;
}

const Footer: React.FC<FooterProps> = ({
  links = footerNavigationItems,
  socialLinks = {
    twitter: "#",
    instagram: "#",
    facebook: "#",
  },
  copyrightText = "@2023 Trendify. All rights reserved."
}) => {
  return (
    <Box sx={{ bgcolor: 'background.default', py: 5 }}>
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 3,
            textAlign: 'center',
          }}
        >
          {/* Footer Links */}
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              justifyContent: 'center',
              gap: { xs: 3, md: 6 },
            }}
          >
            {links.map((link) => (
              <Link key={link.label} href={link.href} style={{ textDecoration: 'none' }}>
                <Button
                  sx={{
                    color: 'text.secondary',
                    fontSize: '1rem',
                    fontWeight: 400,
                    textTransform: 'none',
                    minWidth: 160,
                    p: 0,
                    '&:hover': {
                      backgroundColor: 'transparent',
                      color: 'primary.main',
                    },
                  }}
                >
                  {link.label}
                </Button>
              </Link>
            ))}
          </Box>

          {/* Social Media Icons */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: 2,
            }}
          >
            {socialLinks.twitter && (
              <IconButton
                href={socialLinks.twitter}
                sx={{
                  color: 'text.secondary',
                  '&:hover': {
                    color: 'primary.main',
                  },
                }}
              >
                <TwitterIcon />
              </IconButton>
            )}
            
            {socialLinks.instagram && (
              <IconButton
                href={socialLinks.instagram}
                sx={{
                  color: 'text.secondary',
                  '&:hover': {
                    color: 'primary.main',
                  },
                }}
              >
                <InstagramIcon />
              </IconButton>
            )}
            
            {socialLinks.facebook && (
              <IconButton
                href={socialLinks.facebook}
                sx={{
                  color: 'text.secondary',
                  '&:hover': {
                    color: 'primary.main',
                  },
                }}
              >
                <FacebookIcon />
              </IconButton>
            )}
          </Box>

          {/* Copyright */}
          <Typography
            variant="body1"
            sx={{
              color: 'text.secondary',
              lineHeight: 1.5,
            }}
          >
            {copyrightText}
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
