'use client';

import React from 'react';
import { Box } from '@mui/material';
import Header from './Header';
import Footer from './Footer';

interface LayoutProps {
  children: React.ReactNode;
  headerProps?: Record<string, unknown>;
  footerProps?: Record<string, unknown>;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  headerProps = {},
  footerProps = {}
}) => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.default',
      }}
    >
      <Header {...headerProps} />
      
      <Box component="main" sx={{ flexGrow: 1 }}>
        {children}
      </Box>
      
      <Footer {...footerProps} />
    </Box>
  );
};

export default Layout;
