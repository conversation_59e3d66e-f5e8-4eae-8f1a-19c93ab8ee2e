'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  AppBar,
  Toolbar,
  Box,
  Typography,
  Button,
  IconButton,
  Avatar,
  useTheme,
  Menu,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemText,
  useMediaQuery,
} from '@mui/material';
import {
  Search as SearchIcon,
  ShoppingBag as ShoppingBagIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { mainNavigationItems, userNavigationItems, NavigationItem } from '../../data/navigationData';

interface HeaderProps {
  logo?: React.ReactNode;
  brandName?: string;
  navigationItems?: NavigationItem[];
  userAvatar?: string;
}

const Header: React.FC<HeaderProps> = ({
  logo,
  brandName = "Trendify",
  navigationItems = mainNavigationItems,
  userAvatar = "https://lh3.googleusercontent.com/aida-public/AB6AXuDWt3gN5Y-K95fTJYAbxmDCLEB4finCud-1mpdK87VFWXI9NQdf3xXcPVGZ349Fetxfz2AnpczIHp1U72vjei3Bc8MZqdgRR-RtnHUSBODfsm0TlyvC8kQ7VQQmAz077P5UVi2C-nn06D_4VZEpKIWRnlyivfT44wmHPAKsG6aKqpZ6fUDhoYOYyk8L1uPnKqAhXVKTsFtbzkHUqW73L-OBK4S81eDgtdAYV5hKWATQhMH7dFvqf3Kb13oqVVjzeSrl4qe0_rGtYaQa"
}) => {
  const theme = useTheme();
  const router = useRouter();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileOpen, setMobileOpen] = useState(false);

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSearchClick = () => {
    router.push('/searchpage');
  };

  const handleMobileMenuToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMobileMenuClose = () => {
    setMobileOpen(false);
  };

  const defaultLogo = (
    <svg
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ width: 16, height: 16 }}
    >
      <path
        d="M4 4H17.3334V17.3334H30.6666V30.6666H44V44H4V4Z"
        fill="currentColor"
      />
    </svg>
  );

  const mobileMenu = (
    <Drawer
      anchor="right"
      open={mobileOpen}
      onClose={handleMobileMenuClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: 280,
          boxSizing: 'border-box',
          pt: 2,
        },
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 2, pb: 0 }}>
        <IconButton onClick={handleMobileMenuClose}>
          <CloseIcon />
        </IconButton>
      </Box>
      
      <List>
        {navigationItems.map((item) => (
          <ListItem key={item.label} sx={{ px: 3 }}>
            <Link 
              href={item.href} 
              style={{ textDecoration: 'none', color: 'inherit', width: '100%' }}
              onClick={handleMobileMenuClose}
            >
              <ListItemText 
                primary={item.label}
                sx={{
                  '& .MuiListItemText-primary': {
                    fontSize: '1.1rem',
                    fontWeight: 500,
                  }
                }}
              />
            </Link>
          </ListItem>
        ))}
        
        <Box sx={{ borderTop: '1px solid', borderColor: 'divider', mt: 2, pt: 2 }}>
          {userNavigationItems.map((item) => (
            <ListItem key={item.label} sx={{ px: 3 }}>
              <Link 
                href={item.href} 
                style={{ textDecoration: 'none', color: 'inherit', width: '100%' }}
                onClick={handleMobileMenuClose}
              >
                <ListItemText 
                  primary={item.label}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontSize: '1rem',
                      color: 'text.secondary',
                    }
                  }}
                />
              </Link>
            </ListItem>
          ))}
        </Box>
      </List>
    </Drawer>
  );

  return (
    <AppBar 
      position="static" 
      elevation={0}
      sx={{
        bgcolor: 'background.default',
        borderBottom: `1px solid ${theme.palette.primary.light}`,
        color: 'text.primary',
      }}
    >
      <Toolbar sx={{ px: { xs: 2, md: 5 }, py: 1.5 }}>
        {/* Logo and Brand */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Link href="/" style={{ display: 'flex', alignItems: 'center', gap: 8, textDecoration: 'none', color: 'inherit' }}>
            {logo || defaultLogo}
            <Typography 
              variant="h2" 
              component="h1"
              sx={{ 
                fontSize: { xs: '1rem', sm: '1.125rem' },
                fontWeight: 700,
                color: 'text.primary'
              }}
            >
              {brandName}
            </Typography>
          </Link>
        </Box>

        {/* Navigation */}
        <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'flex-end', gap: 4 }}>
          {/* Desktop Navigation */}
          <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 4.5 }}>
            {navigationItems.map((item) => (
              <Link key={item.label} href={item.href} style={{ textDecoration: 'none' }}>
                <Button
                  sx={{
                    color: 'text.primary',
                    fontSize: '0.875rem',
                    fontWeight: 500,
                    textTransform: 'none',
                    minWidth: 'auto',
                    p: 0,
                    '&:hover': {
                      backgroundColor: 'transparent',
                      color: 'primary.main',
                    },
                  }}
                >
                  {item.label}
                </Button>
              </Link>
            ))}
          </Box>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <IconButton
              onClick={handleSearchClick}
              sx={{
                bgcolor: 'primary.light',
                color: 'text.primary',
                '&:hover': {
                  bgcolor: 'primary.light',
                  opacity: 0.8,
                },
                width: { xs: 36, sm: 40 },
                height: { xs: 36, sm: 40 },
              }}
            >
              <SearchIcon fontSize="small" />
            </IconButton>
            
            <IconButton
              sx={{
                bgcolor: 'primary.light',
                color: 'text.primary',
                '&:hover': {
                  bgcolor: 'primary.light',
                  opacity: 0.8,
                },
                width: { xs: 36, sm: 40 },
                height: { xs: 36, sm: 40 },
              }}
            >
              <ShoppingBagIcon fontSize="small" />
            </IconButton>

            {/* Mobile Menu Button */}
            {isMobile && (
              <IconButton
                onClick={handleMobileMenuToggle}
                sx={{
                  ml: 1,
                  width: { xs: 36, sm: 40 },
                  height: { xs: 36, sm: 40 },
                }}
              >
                <MenuIcon />
              </IconButton>
            )}

            {/* Desktop User Menu */}
            {!isMobile && (
              <IconButton
                onClick={handleUserMenuOpen}
                sx={{
                  width: 40,
                  height: 40,
                  ml: 1,
                  p: 0,
                }}
              >
                <Avatar
                  src={userAvatar}
                  sx={{ width: 40, height: 40 }}
                />
              </IconButton>
            )}

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleUserMenuClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              {userNavigationItems.map((item) => (
                <MenuItem key={item.label} onClick={handleUserMenuClose}>
                  <Link 
                    href={item.href} 
                    style={{ 
                      textDecoration: 'none', 
                      color: 'inherit',
                      width: '100%'
                    }}
                  >
                    {item.label}
                  </Link>
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </Box>
      </Toolbar>

      {/* Mobile Menu Drawer */}
      {mobileMenu}
    </AppBar>
  );
};

export default Header;
