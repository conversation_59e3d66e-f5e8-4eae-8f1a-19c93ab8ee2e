import SchemaBuilder from '@pothos/core'
import PrismaPlugin from '@pothos/plugin-prisma'
import ValidationPlugin from '@pothos/plugin-validation'
import ErrorsPlugin from '@pothos/plugin-errors'
import RelayPlugin from '@pothos/plugin-relay'
import type PrismaTypes from '../generated/pothos-types'
import { prisma } from './prisma'

export const builder = new SchemaBuilder<{
  PrismaTypes: PrismaTypes
  Context: {
    prisma: typeof prisma
  }
  Scalars: {
    DateTime: { Input: Date; Output: Date }
    UUID: { Input: string; Output: string }
  }
}>({
  plugins: [PrismaPlugin, ValidationPlugin, ErrorsPlugin, RelayPlugin],
  prisma: {
    client: prisma,
    // Configure error handling
    onUnusedQuery: process.env.NODE_ENV === 'production' ? null : 'warn',
    // Configure pagination
    maxConnectionSize: 100,
    defaultConnectionSize: 20,
  },
  errors: {
    defaultTypes: [Error],
  },
  relay: {
    clientMutationId: 'omit',
    cursorType: 'String',
  },
})

// Define DateTime scalar
builder.scalarType('DateTime', {
  serialize: (date) => date.toISOString(),
  parseValue: (value) => {
    if (typeof value === 'string') {
      return new Date(value)
    }
    throw new Error('Invalid DateTime value')
  },
})

// Define UUID scalar
builder.scalarType('UUID', {
  serialize: (uuid) => uuid,
  parseValue: (value) => {
    if (typeof value === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value)) {
      return value
    }
    throw new Error('Invalid UUID format')
  },
})

// Define base Error type for error handling
builder.objectType(Error, {
  name: 'Error',
  fields: (t) => ({
    message: t.exposeString('message'),
  }),
})
