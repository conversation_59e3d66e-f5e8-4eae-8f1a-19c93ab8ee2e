generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator pothos {
  provider = "prisma-pothos-types" // built into @pothos/plugin-prisma
  output   = "../src/generated/pothos-types.ts" // optional but handy
}

// Purpose : Stores user information for registered customers.

model Customer {
  id        String   @id @default(uuid()) @map("_id")
  email     String   @unique
  firstName String
  lastName  String
  phone     String?
  createdAt DateTime @default(now())

  wishlist        Wishlist? // One-to-one relationship
  reviews         Review[] // One-to-many relationship
  orders          Order[] // One-to-many relationship
  paymentMethods  PaymentMethod[] // One-to-many relationship
  promoCodeUsages PromoCodeUsage[] // One-to-many relationship
}

// Purpose : Stores a customer's saved items they want to buy later.

model Wishlist {
  id         String         @id @default(uuid()) @map("_id")
  customer   Customer       @relation(fields: [customerId], references: [id])
  customerId String         @unique
  items      WishlistItem[]
}

// Purpose : Individual item in a wishlist linked to a product variant.

model WishlistItem {
  id               String         @id @default(uuid()) @map("_id")
  wishlist         Wishlist       @relation(fields: [wishlistId], references: [id])
  wishlistId       String
  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId String
  addedAt          DateTime       @default(now())
}

// Purpose : Stores customer feedback and ratings for products.

model Review {
  id         String   @id @default(uuid()) @map("_id")
  customer   Customer @relation(fields: [customerId], references: [id])
  customerId String
  product    Product  @relation(fields: [productId], references: [id])
  productId  String
  rating     Int      @default(5)
  comment    String?
  images     String[]
  createdAt  DateTime @default(now())
}

// Purpose : Stores promotional/discount codes for marketing campaigns.

model PromoCode {
  id            String           @id @default(uuid()) @map("_id")
  code          String           @unique
  discountType  String // "fixed", "percent"
  discountValue Float
  expiration    DateTime
  usageLimit    Int?
  usedCount     Int              @default(0)
  isActive      Boolean          @default(true)
  usages        PromoCodeUsage[]
}

// need to discuss on this model

model PromoCodeUsage {
  id          String    @id @default(uuid()) @map("_id")
  promoCode   PromoCode @relation(fields: [promoCodeId], references: [id])
  promoCodeId String
  customer    Customer  @relation(fields: [customerId], references: [id])
  customerId  String
  usedAt      DateTime  @default(now())
}

// Purpose : Improves product discoverability and SEO by tagging products with keywords.

model Tag {
  id          String       @id @default(uuid()) @map("_id")
  name        String       @unique
  productTags ProductTag[]
}

// Junction table for Product-Tag many-to-many relationship
model ProductTag {
  id        String  @id @default(uuid()) @map("_id")
  product   Product @relation(fields: [productId], references: [id])
  productId String
  tag       Tag     @relation(fields: [tagId], references: [id])
  tagId     String

  @@unique([productId, tagId])
}

// Purpose : Stores customer payment methods like UPI or card details.

model PaymentMethod {
  id           String               @id @default(uuid()) @map("_id")
  customer     Customer             @relation(fields: [customerId], references: [id])
  customerId   String
  provider     String // e.g., "razorpay", "stripe", "phonepe"
  type         String // "card", "upi"
  last4        String // last 4 digits of card/UPI handle
  isDefault    Boolean              @default(false)
  metadata     Json?
  createdAt    DateTime             @default(now())
  transactions PaymentTransaction[]
}

// Purpose : Logs each transaction including UPI transaction ID.

model PaymentTransaction {
  id               String        @id @default(uuid()) @map("_id")
  paymentMethod    PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  paymentMethodId  String
  order            Order?        @relation(fields: [orderId], references: [id])
  orderId          String?       @unique
  amount           Float
  status           String // success / failed / pending
  transactionId    String // gateway transaction ID (like UPI TXN ID)
  providerResponse Json?
  createdAt        DateTime      @default(now())
}

// Purpose : Tracks customer purchases.

model Order {
  id                 String              @id @default(uuid()) @map("_id")
  customer           Customer            @relation(fields: [customerId], references: [id])
  customerId         String
  orderDate          DateTime            @default(now())
  totalAmount        Float
  status             OrderStatus         @default(pending)
  items              OrderItem[]
  paymentTransaction PaymentTransaction?
}

// Purpose : Represents individual items in an order.

model OrderItem {
  id               String         @id @default(uuid()) @map("_id")
  order            Order          @relation(fields: [orderId], references: [id])
  orderId          String
  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId String
  quantity         Int
  priceAtPurchase  Float
}

// Purpose : Main product listing with variants and metadata.

model Product {
  id          String           @id @default(uuid()) @map("_id")
  name        String
  description String
  category    Category         @relation(fields: [categoryId], references: [id])
  categoryId  String
  variants    ProductVariant[]
  images      ProductImage[]
  tags        ProductTag[]
  reviews     Review[]
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
}

// Purpose : Stores image URLs for each product.

model ProductImage {
  id        String  @id @default(uuid()) @map("_id")
  product   Product @relation(fields: [productId], references: [id])
  productId String
  imageUrl  String
  isPrimary Boolean @default(false)
}

// Purpose : Available sizes for products (e.g., S, M, L).

model Size {
  id        String           @id @default(uuid()) @map("_id")
  sizeLabel String           @unique
  variants  ProductVariant[]
}

// Purpose : Available colors for products.

model Color {
  id        String           @id @default(uuid()) @map("_id")
  colorName String           @unique
  hexCode   String?
  variants  ProductVariant[]
}

// Purpose : Unique combination of size, color, and pricing for a product.

model ProductVariant {
  id            String            @id @default(uuid()) @map("_id")
  product       Product           @relation(fields: [productId], references: [id])
  productId     String
  size          Size              @relation(fields: [sizeId], references: [id])
  sizeId        String
  color         Color             @relation(fields: [colorId], references: [id])
  colorId       String
  sku           String            @unique
  price         Float
  quantity      Int
  discounts     VariantDiscount[]
  wishlistItems WishlistItem[]
  orderItems    OrderItem[]

  @@unique([productId, sizeId, colorId])
}

// Purpose : General discount rules applied to variants.

model Discount {
  id              String            @id @default(uuid()) @map("_id")
  name            String
  description     String?
  discountPercent Int?
  discountAmount  Int?
  startDate       DateTime
  endDate         DateTime
  variants        VariantDiscount[]
}

// Purpose : Links specific product variants to active discounts.

model VariantDiscount {
  id               String         @id @default(uuid()) @map("_id")
  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id])
  productVariantId String
  discount         Discount       @relation(fields: [discountId], references: [id])
  discountId       String

  @@unique([productVariantId, discountId])
}

// Purpose : Organizes products into categories (e.g., T-shirts, Jeans).

model Category {
  id       String    @id @default(uuid()) @map("_id")
  name     String    @unique
  products Product[]
}

// Purpose : Status tracking for order lifecycle.

enum OrderStatus {
  pending
  shipped
  delivered
  cancelled
}
